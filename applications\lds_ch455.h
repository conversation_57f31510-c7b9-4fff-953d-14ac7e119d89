/**
 * @file lds_ch455.h
 * @brief CH455芯片驱动头文件
 * @details 全面的基于I2C的CH455按键扫描器和LED控制器驱动
 *          支持按键扫描、LED/显示控制和中断处理
 * <AUTHOR> Team
 * @version 1.0
 * @date 2025-07-10
 *
 * @copyright Copyright (c) 2025 LDS
 *
 * 功能特性:
 * - 基于I2C的通信，具有全面的错误处理
 * - 带消抖和回调支持的按键扫描
 * - 带亮度调节的LED/显示控制
 * - 使用RT-Thread同步的线程安全操作
 * - 配置参数和性能监控
 * - 用于调试和测试的MSH命令
 * - 多实例支持
 * - 强大的错误恢复机制
 */
#ifndef __APPLICATIONS_LDS_CH455_H__
#define __APPLICATIONS_LDS_CH455_H__

#include <stdint.h>
#include <stdbool.h>
#include <rtthread.h>

#ifdef __cplusplus
extern "C" {
#endif

/* CH455 I2C配置 */
#define LDS_CH455_I2C_ADDR_DEFAULT      (0x40)      /**< 默认I2C从设备地址 */
#define LDS_CH455_I2C_MASK              (0x3E)      /**< 命令的I2C地址掩码 */
#define LDS_CH455_I2C_TIMEOUT_MS        (100)       /**< I2C操作超时时间 */
#define LDS_CH455_I2C_RETRY_COUNT       (3)         /**< I2C重试次数 */

/* CH455系统控制位 */
#define LDS_CH455_BIT_ENABLE            (0x01)      /**< 使能/禁用位 */
#define LDS_CH455_BIT_SLEEP             (0x04)      /**< 睡眠控制位 */
#define LDS_CH455_BIT_7SEG              (0x08)      /**< 7段显示模式位 */
#define LDS_CH455_BIT_INTENS1           (0x10)      /**< 亮度级别1 */
#define LDS_CH455_BIT_INTENS2           (0x20)      /**< 亮度级别2 */
#define LDS_CH455_BIT_INTENS3           (0x30)      /**< 亮度级别3 */
#define LDS_CH455_BIT_INTENS4           (0x40)      /**< 亮度级别4 */
#define LDS_CH455_BIT_INTENS5           (0x50)      /**< 亮度级别5 */
#define LDS_CH455_BIT_INTENS6           (0x60)      /**< 亮度级别6 */
#define LDS_CH455_BIT_INTENS7           (0x70)      /**< 亮度级别7 */
#define LDS_CH455_BIT_INTENS8           (0x00)      /**< 亮度级别8（最大） */

/* CH455系统命令（16位） */
#define LDS_CH455_SYSOFF                (0x0400)    /**< 关闭显示和键盘 */
#define LDS_CH455_SYSON                 (LDS_CH455_SYSOFF | LDS_CH455_BIT_ENABLE)  /**< 打开显示和键盘 */
#define LDS_CH455_SLEEPOFF              LDS_CH455_SYSOFF    /**< 关闭睡眠模式 */
#define LDS_CH455_SLEEPON               (LDS_CH455_SYSOFF | LDS_CH455_BIT_SLEEP)   /**< 打开睡眠模式 */
#define LDS_CH455_7SEG_ON               (LDS_CH455_SYSON | LDS_CH455_BIT_7SEG)     /**< 启用7段显示模式 */
#define LDS_CH455_8SEG_ON               (LDS_CH455_SYSON | 0x00)                   /**< 启用8段显示模式 */
#define LDS_CH455_SYSON_4               (LDS_CH455_SYSON | LDS_CH455_BIT_INTENS4)  /**< 系统开启，亮度4 */
#define LDS_CH455_SYSON_8               (LDS_CH455_SYSON | LDS_CH455_BIT_INTENS8)  /**< 系统开启，亮度8 */

/* CH455显示寄存器命令（16位） */
#define LDS_CH455_DIG0                  (0x1400)    /**< 显示数字0寄存器 */
#define LDS_CH455_DIG1                  (0x1500)    /**< 显示数字1寄存器 */
#define LDS_CH455_DIG2                  (0x1600)    /**< 显示数字2寄存器 */
#define LDS_CH455_DIG3                  (0x1700)    /**< 显示数字3寄存器 */

/* CH455按键扫描命令（16位） */
#define LDS_CH455_GET_KEY               (0x0700)    /**< 获取按键扫描结果 */

/* CH455传统命令定义（向后兼容） */
#define LDS_CH455_CMD_RESET             LDS_CH455_SYSOFF    /**< 复位命令（关闭系统） */
#define LDS_CH455_CMD_SLEEP             LDS_CH455_SLEEPON   /**< 睡眠模式命令 */
#define LDS_CH455_CMD_SYSTEM_EN         LDS_CH455_SYSON     /**< 系统使能命令 */

/* CH455配置参数 */
#define LDS_CH455_MAX_DIGITS            (2)         /**< 最大数字位数 */
#define LDS_CH455_MAX_KEYS              (20)        /**< 最大按键数量 */
#define LDS_CH455_DEBOUNCE_TIME_MS      (50)        /**< 按键消抖时间 */
#define LDS_CH455_SCAN_INTERVAL_MS      (10)        /**< 按键扫描间隔 */
#define LDS_CH455_MAX_BRIGHTNESS        (8)         /**< 最大亮度级别 */

#define LDS_CH455_TICK_TO_MS(tick)      ((tick) * 1000 / RT_TICK_PER_SECOND)

/**
 * @brief CH455按键事件类型
 */
typedef enum
{
    LDS_CH455_KEY_PRESSED = 0,      /**< 按键按下事件 */
    LDS_CH455_KEY_RELEASED,         /**< 按键释放事件 */
    LDS_CH455_KEY_LONG_PRESSED,     /**< 长按事件 */
} lds_ch455_key_event_t;

/**
 * @brief CH455操作模式
 */
typedef enum
{
    LDS_CH455_MODE_DISPLAY_ONLY = 0,    /**< 仅显示模式（7/8段显示） */
    LDS_CH455_MODE_KEYBOARD_ONLY,       /**< 仅键盘模式（按键扫描） */
    LDS_CH455_MODE_COMBINED,            /**< 组合模式（显示+键盘） */
} lds_ch455_mode_t;

/**
 * @brief CH455错误代码
 */
typedef enum
{
    LDS_CH455_ERR_NONE = 0,         /**< 无错误 */
    LDS_CH455_ERR_I2C_FAIL = -1,    /**< I2C通信失败 */
    LDS_CH455_ERR_TIMEOUT = -2,     /**< 操作超时 */
    LDS_CH455_ERR_INVALID_PARAM = -3, /**< 无效参数 */
    LDS_CH455_ERR_NOT_INIT = -4,    /**< 设备未初始化 */
    LDS_CH455_ERR_BUSY = -5,        /**< 设备忙 */
} lds_ch455_error_t;

/**
 * @brief CH455按键回调函数类型
 * @param key_code 按键代码（0-19）
 * @param event 按键事件类型
 * @param user_data 用户数据指针
 */
typedef void (*lds_ch455_key_callback_t)(uint8_t key_code, lds_ch455_key_event_t event, void *user_data);

/**
 * @brief CH455配置结构体
 */
typedef struct
{
    const char *i2c_bus_name;           /**< I2C总线设备名称 */
    uint8_t i2c_addr;                   /**< I2C从设备地址 */
    uint32_t i2c_timeout_ms;            /**< I2C超时时间（毫秒） */
    uint8_t i2c_retry_count;            /**< I2C重试次数 */
    lds_ch455_mode_t mode;              /**< 操作模式 */
    uint8_t brightness;                 /**< 初始亮度（0-8） */
    uint32_t scan_interval_ms;          /**< 按键扫描间隔 */
    uint32_t debounce_time_ms;          /**< 按键消抖时间 */
    bool auto_scan_enable;              /**< 启用自动按键扫描 */
    lds_ch455_key_callback_t key_callback; /**< 按键事件回调 */
    void *user_data;                    /**< 回调的用户数据 */
} lds_ch455_config_t;

/**
 * @brief CH455统计信息结构体
 */
typedef struct
{
    volatile uint32_t i2c_tx_count;     /**< I2C发送计数 */
    volatile uint32_t i2c_rx_count;     /**< I2C接收计数 */
    volatile uint32_t i2c_error_count;  /**< I2C错误计数 */
    volatile uint32_t key_press_count;  /**< 按键按下计数 */
    volatile uint32_t key_scan_count;   /**< 按键扫描计数 */
    volatile uint32_t callback_count;   /**< 回调调用计数 */
    rt_tick_t last_key_time;            /**< 最后按键事件时间戳 */
    rt_tick_t last_scan_time;           /**< 最后扫描时间戳 */
} lds_ch455_stats_t;

/**
 * @brief CH455设备句柄（不透明）
 */
typedef struct lds_ch455_device *lds_ch455_handle_t;

/**
 * @brief 初始化CH455设备
 * @details 使用指定配置创建并初始化CH455设备实例
 *
 * @param config 指向配置结构体的指针
 * @return lds_ch455_handle_t 成功时返回设备句柄，失败时返回NULL
 *
 * @note 此函数执行以下操作：
 *       - I2C总线初始化和设备检测
 *       - CH455芯片复位和配置
 *       - 按键扫描线程创建（如果启用）
 *       - 回调注册
 *
 * @warning 调用此函数前请确保I2C总线可用
 */
lds_ch455_handle_t ldsCh455Init(const lds_ch455_config_t *config);

/**
 * @brief 反初始化CH455设备
 * @details 清理设备资源并停止所有操作
 *
 * @param handle 设备句柄
 * @return int 成功返回0，错误返回负值
 *
 * @note 此函数执行完整的清理，包括：
 *       - 停止按键扫描线程
 *       - 释放I2C资源
 *       - 释放分配的内存
 */
int ldsCh455Deinit(lds_ch455_handle_t handle);

/**
 * @brief 设置LED/显示器亮度
 * @details 控制LED或7段显示器的亮度
 *
 * @param handle 设备句柄
 * @param brightness 亮度级别（0-8，0=关闭，8=最大）
 * @return int 成功返回0，错误返回负值
 *
 * @note 亮度设置影响所有连接的LED/显示器
 */
int ldsCh455SetBrightness(lds_ch455_handle_t handle, uint8_t brightness);

/**
 * @brief 在7段显示器上显示数字
 * @details 在指定显示位置显示数字（0-F）或字符
 *
 * @param handle 设备句柄
 * @param digit_pos 显示位置（0-3）
 * @param value 要显示的值（0-F表示数字，或自定义段模式）
 * @param decimal_point 启用小数点
 * @return int 成功返回0，错误返回负值
 */
int ldsCh455DisplayDigit(lds_ch455_handle_t handle, uint8_t digit_pos, uint8_t value, bool decimal_point);

/**
 * @brief 在7段显示器上显示数字
 * @details 在可用显示位置显示多位数字
 *
 * @param handle 设备句柄
 * @param number 要显示的数字
 * @return int 成功返回0，错误返回负值
 */
int ldsCh455DisplayNumber(lds_ch455_handle_t handle, uint16_t number);

/**
 * @brief 控制单个LED
 * @details 打开/关闭单个LED或设置自定义段模式
 *
 * @param handle 设备句柄
 * @param led_index LED索引（0-3表示数字位置）
 * @param pattern LED段模式（位字段）
 * @return int 成功返回0，错误返回负值
 */
int ldsCh455SetLed(lds_ch455_handle_t handle, uint8_t led_index, uint8_t pattern);

/**
 * @brief 手动扫描按键
 * @details 执行单次按键扫描操作并返回按键状态
 *
 * @param handle 设备句柄
 * @param key_state 存储按键状态的指针（20位字段）
 * @return int 成功返回0，错误返回负值
 *
 * @note 当禁用自动扫描时，此函数很有用
 */
int ldsCh455ScanKeys(lds_ch455_handle_t handle, uint32_t *key_state);

/**
 * @brief 获取设备统计信息
 * @details 检索性能和使用统计信息
 *
 * @param handle 设备句柄
 * @param stats 指向统计信息结构体的指针
 * @return int 成功返回0，错误返回负值
 */
int ldsCh455GetStats(lds_ch455_handle_t handle, lds_ch455_stats_t *stats);

/**
 * @brief 重置设备统计信息
 * @details 清除所有性能计数器
 *
 * @param handle 设备句柄
 * @return int 成功返回0，错误返回负值
 */
int ldsCh455ResetStats(lds_ch455_handle_t handle);

/**
 * @brief 检查设备健康状态
 * @details 执行设备健康检查和通信测试
 *
 * @param handle 设备句柄
 * @return int 健康返回0，警告返回正值，错误返回负值
 */
int ldsCh455CheckHealth(lds_ch455_handle_t handle);

/**
 * @brief 设置操作模式
 * @details 更改CH455设备的操作模式
 *
 * @param handle 设备句柄
 * @param mode 新的操作模式
 * @return int 成功返回0，错误返回负值
 *
 * @note 此函数将：
 *       - 停止当前操作
 *       - 为新模式重新配置硬件
 *       - 重启适当的服务（显示/键盘）
 *       - 仅初始化所选模式所需的硬件
 */
int ldsCh455SetMode(lds_ch455_handle_t handle, lds_ch455_mode_t mode);

/**
 * @brief 获取当前操作模式
 * @details 返回设备的当前操作模式
 *
 * @param handle 设备句柄
 * @param mode 存储当前模式的指针
 * @return int 成功返回0，错误返回负值
 */
int ldsCh455GetMode(lds_ch455_handle_t handle, lds_ch455_mode_t *mode);

#ifdef __cplusplus
}
#endif

#endif /* __APPLICATIONS_LDS_CH455_H__ */
