/**
 * @file lds_uac.h
 * @brief UAC (USB音频类) 控制接口头文件
 * @details 此文件包含UAC控制接口函数的声明
 * <AUTHOR> Team
 * @version 1.0
 * @date 2025-07-10
 *
 * @copyright Copyright (c) 2025 LDS
 *
 */
#ifndef __APPLICATIONS_LDS_UAC_H__
#define __APPLICATIONS_LDS_UAC_H__

#include <stdint.h>
#include <stdbool.h>

#ifdef __cplusplus
extern "C" {
#endif


/**
 * @brief 向UAC设备发送按键命令
 * @param key_down true表示按键按下，false表示按键释放
 * @return 无返回值
 */
void ldsUacKeyCmdsend(bool key_down);

/**
 * @brief 初始化UAC设备
 * @return 成功返回0，错误返回负值
 */
int ldsUacInit(void);

#ifdef __cplusplus
}
#endif

#endif /* __APPLICATIONS_LDS_UAC_H__ */