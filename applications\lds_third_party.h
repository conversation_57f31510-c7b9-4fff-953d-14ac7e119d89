/**
 * @file lds_third_party.h
 * @brief LDS第三方通信协议栈头文件
 * @details 此头文件包含第三方通信协议函数的声明
 *          和遵循指定大端字节序协议格式的数据结构
 * <AUTHOR> Team
 * @version 1.0
 * @date 2025-07-23
 *
 * @copyright Copyright (c) 2025 LDS
 *
 * 协议格式:
 * Head(2) + CMD(1) + SEQ(1) + LEN(2) + DATA(可变) + CRC16(2)
 * - 大端字节序（MSB在前）
 * - Head: 所有帧固定为0x5AA5
 * - SEQ: 0x00-0xFF序列号，用于请求/响应匹配
 * - CMD: 定义操作的功能码
 * - LEN: DATA字段的字节长度
 * - DATA: 可变长度载荷数据
 * - CRC16: 16位CRC校验和（从Head到CRC16-1所有字节的CRC16）
 */

#ifndef __APPLICATIONS_LDS_THIRD_PARTY_H__
#define __APPLICATIONS_LDS_THIRD_PARTY_H__

/* ================================ Includes ================================ */
#include <stdint.h>
#include <stdbool.h>
#include <rtthread.h>

#ifdef __cplusplus
extern "C" {
#endif

/* ================================ Macros ================================== */

/* ================================ Protocol Constants ===================== */
#define THIRD_PARTY_FRAME_HEAD1          0x5A
#define THIRD_PARTY_FRAME_HEAD2          0xA5
#define THIRD_PARTY_MIN_FRAME_LEN       8      /**< 不含DATA的最小帧长度 */
#define THIRD_PARTY_MAX_FRAME_LEN       128     /**< 最大帧长度 */
#define THIRD_PARTY_MAX_DATA_LEN        (THIRD_PARTY_MAX_FRAME_LEN - THIRD_PARTY_MIN_FRAME_LEN)

/* ================================ 类型定义 ======================== */

/**
 * @brief 第三方命令枚举
 * @details 定义第三方通信支持的命令类型
 */
typedef enum {
    LDS_THIRD_PARTY_CMD_MUTE = 0x01,          /**< 无线麦克风静音命令 */
    LDS_THIRD_PARTY_CMD_EFFECT = 0x02,        /**< 无线麦克风效果命令 */
    LDS_THIRD_PARTY_CMD_POWER_CTRL = 0x03,    /**< 电源控制命令 */
    LDS_THIRD_PARTY_CMD_VERSION = 0x04,       /**< 查询版本信息命令 */
    LDS_THIRD_PARTY_CMD_STATUS = 0x05,       /**< 查询状态信息命令 */
    LDS_THIRD_PARTY_CMD_MAX,
} LDS_THIRD_PARTY_CMD_E;

/**
 * @brief 协议帧结构体
 * @details 表示完整协议帧的结构体
 */
typedef struct {
    uint8_t head1;                           /**< 帧头（0x5A） */
    uint8_t head2;                           /**< 帧头（0xA5） */
    uint8_t cmd;                           /**< 命令码（大端序） */
    uint8_t seq;                            /**< 序列号 */
    uint16_t dataLen;                       /**< 数据长度（大端序） */
    uint8_t data[THIRD_PARTY_MAX_DATA_LEN];  /**< 数据载荷 */
    uint16_t crc16;                         /**< 帧CRC16校验和 */
} lds_third_party_frame_t;

/* ================================ 函数声明 =================== */

/**
 * @brief 初始化第三方通信系统
 * @details 初始化硬件、UART通信、定时器和状态机
 *
 * @return int 成功返回0，失败返回负错误码
 *
 * @note 此函数执行完整的第三方系统初始化，包括：
 *       - 电源控制引脚设置
 *       - 带回调的UART接口初始化
 *       - 心跳和命令重传的定时器配置
 *       - 线程安全的互斥锁初始化
 *       - 状态机和命令队列初始化
 *
 * @example
 * @code
 * int result = ldsThirdPartyInit();
 * if (result != 0) {
 *     rt_kprintf("Third party initialization failed: %d\n", result);
 * }
 * @endcode
 */
int ldsThirdPartyInit(void);

/**
 * @brief 反初始化第三方通信系统
 * @details 清理所有资源并停止通信
 *
 * @return int 成功返回0，失败返回负错误码
 *
 * @note 此函数应在系统关闭前或不再需要第三方通信时调用
 */
int ldsThirdPartyDeinit(void);

#ifdef __cplusplus
}
#endif

#endif /* __APPLICATIONS_LDS_THIRD_PARTY_H__ */
