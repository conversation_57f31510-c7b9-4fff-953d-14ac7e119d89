/**
 * @file lds_smart_base.c
 * @brief LDS智能底座通信协议栈实现
 * @details 此文件实现了智能底座设备的完整通信协议栈
 *          遵循指定的大端字节序协议格式。
 *          此版本包含命令队列，用于强健的多命令处理。
 * <AUTHOR> Team
 * @version 1.0
 * @date 2025-07-23
 *
 * @copyright Copyright (c) 2025 LDS
 *
 * 协议格式:
 * Head(2) + CMD(1) + SEQ(1) + LEN(2) + DATA(可变) + CRC16(2)
 * - 大端字节序（MSB在前）
 * - Head: 所有帧固定为0x5AA5
 * - SEQ: 0x00-0xFF序列号，用于请求/响应匹配
 * - CMD: 定义操作的功能码
 * - LEN: DATA字段的字节长度
 * - DATA: 可变长度载荷数据
 * - CRC16: 16位CRC校验和（从Head到CRC16-1所有字节的CRC16）
 */

#include <rtdevice.h>
#include <stdlib.h>
#include "lds_utils.h"
#include "lds_uart.h"
#include "lds_smart_base.h"
#include "lds_led_config.h"
#include "lds_uac.h"
#include "lds_dsp.h"
#include "lds_at.h"

#define DBG_TAG "S_BASE"
// #define DBG_LVL DBG_INFO
#include <rtdbg.h>

#define SMART_BASE_VERSION_MAX_LEN     16
/* ================================ Configuration ========================== */
#define SMART_BASE_SERIAL_NAME         "uart5"
#define SMART_BASE_POWER_CTRL_PIN      "PD.1"
#define SMART_BASE_CMD_QUEUE_SIZE      8                            /**< 命令队列大小，支持最多8个待处理命令 */
#define SMART_BASE_MAX_ERROR_COUNT     10                           /**< 假定设备未连接前的最大连续错误数 */
#define SMART_BASE_RESPONSE_FLAG       0x80
/* ================================ 超时配置 ================== */
#define SMART_BASE_RESPONSE_TIMEOUT    (RT_TICK_PER_SECOND * 2)    /**< 2秒响应超时 */
#define SMART_BASE_HEARTBEAT_TIMEOUT   (RT_TICK_PER_SECOND * 20)   /**< 20秒心跳超时 */
#define SMART_BASE_PARSE_TIMEOUT       (RT_TICK_PER_SECOND * 1)   /**< 状态机1秒解析超时 */
#define SMART_BASE_RETRY_COUNT         2                           /**< 最大重试次数 */

/* ================================ 全局变量 ======================= */
static rt_base_t g_smartBasePowerCtrl = -1;       /**< 智能底座电源控制引脚 */
static rt_device_t g_smartBaseDev = RT_NULL;       /**< UART设备句柄 */
static struct rt_timer g_heartbeatTimer;    /**< 心跳超时定时器 */
static struct rt_timer g_retransmissionTimer; /**< 命令队列重传定时器 */
static struct rt_timer g_parseTimer;        /**< 状态机解析超时定时器 */
static struct rt_mutex g_smartBaseMutex;          /**< 线程安全互斥锁 */
static uint8_t g_errorCount = 0 ;       /**< 连续错误计数 */
static bool g_enable = true;            /**< 智能底座通信使能标志 */

/* ================================ 协议状态机 ================ */
static lds_smart_base_frame_t g_rxFrame;           /**< 当前接收帧 */
static uint8_t g_rxBuffer[SMART_BASE_MAX_FRAME_LEN]; /**< 帧接收缓冲区 */
static uint16_t g_rxIndex = 0;              /**< 当前接收索引 */
static uint8_t g_currentSeq = 0;            /**< 当前序列号 */

/* ================================ 命令队列管理 ================ */
/**
 * @brief 待处理命令队列条目结构体
 * @details 包含等待ACK的命令的所有信息，包括重试管理。
 */
typedef struct {
    bool active;                            /**< 定义此队列槽是否在使用 */
    uint8_t seq;                            /**< 此命令的唯一序列号 */
    uint8_t cmd;                           /**< 命令码 */
    uint16_t dataLen;                       /**< 数据载荷长度 */
    uint8_t data[SMART_BASE_MAX_DATA_LEN];         /**< 数据载荷 */
    uint8_t retryCount;                     /**< 当前重试计数 */
    rt_tick_t sent_timestamp;               /**< 命令最后发送时的系统时钟 */
} lds_smart_base_cmd_queue_entry_t;

static lds_smart_base_cmd_queue_entry_t g_cmdQueue[SMART_BASE_CMD_QUEUE_SIZE]; /**< 命令队列 */
static void ldsSmartBaseStartRetransmissionTimer(void);
static int ldsSmartBaseSendFrame(uint8_t cmd, const uint8_t *data, uint16_t dataLen);
static void ldsSmartBaseResetStateMachine(void);

static int8_t g_smart_base_select_mode = -1;        //闪避模式 1开 0关
static int8_t g_smart_base_status_active = -1;    //是否收到手持麦克风声音 1有 0无
static bool g_smart_base_status_connect = false;   //是否连接上手持麦克风
static char g_smart_base_version[SMART_BASE_VERSION_MAX_LEN] = {0};

/**
 * @brief 智能底座协议状态枚举
 * @details 定义协议帧解析状态机的状态
 */
typedef enum {
    SMART_BASE_STATE_IDLE = 0,        /**< 等待帧头 */
    SMART_BASE_STATE_HEAD_1,          /**< 接收帧头1 0x5a */
    SMART_BASE_STATE_HEAD_2,          /**< 接收帧头2 0xa5 */
    SMART_BASE_STATE_CMD,           /**< 接收命令字节 */
    SMART_BASE_STATE_SEQ,             /**< 接收序列号 */
    SMART_BASE_STATE_LEN_H,           /**< 接收长度高字节 */
    SMART_BASE_STATE_LEN_L,           /**< 接收长度低字节 */
    SMART_BASE_STATE_DATA,            /**< 接收数据载荷 */
    SMART_BASE_STATE_CRC16_H,         /**< 接收CRC16高字节 */
    SMART_BASE_STATE_CRC16_L,         /**< 接收CRC16低字节 */
} LDS_SMART_BASE_STATE_E;

static LDS_SMART_BASE_STATE_E g_rxState = SMART_BASE_STATE_IDLE;

static void ldsSmartBaseSendSelectCmd(bool active)
{
    if(!g_smart_base_select_mode){
        LOG_I("mode false, send select cmd false");
        ldsDspSetLineSelect(false);
        return;
    }
    LOG_I("send select cmd %d", active);
    ldsDspSetLineSelect(active);
}
/**
 * @brief 重置智能底座设备
 * @details 通过电源控制引脚执行智能底座设备的硬件重置
 */
static void ldsSmartBaseReset(void)
{
    if (g_smartBasePowerCtrl <= 0) {
        LOG_E("SMART_BASE power control pin not initialized");
        return;
    }

    LOG_I("Resetting smart_base");
    rt_pin_write(g_smartBasePowerCtrl, PIN_LOW);
    rt_thread_mdelay(500);
    rt_pin_write(g_smartBasePowerCtrl, PIN_HIGH);
    rt_thread_mdelay(100);
}

/**
 * @brief 心跳超时处理函数
 * @details 当心跳超时发生时调用，触发设备重置
 *
 * @param parameter 定时器参数（未使用）
 */
static void ldsSmartBaseHeartbeatTimeout(void *parameter)
{
    LOG_W("heartbeat timeout, resetting device");
    g_errorCount++;
    if(g_errorCount > SMART_BASE_MAX_ERROR_COUNT){
        LOG_W("SMART_BASE reach max error count,most likely not connected");
        g_enable = false;
        rt_timer_stop(&g_heartbeatTimer);
        return;
    }
    ldsSmartBaseReset();
}

/**
 * @brief 解析超时处理函数
 * @details 当解析超时发生时调用，重置状态机以防止挂起
 *
 * @param parameter 定时器参数（未使用）
 */
static void ldsSmartBaseParseTimeout(void *parameter)
{
    LOG_W("Parse timeout state %d, reset", g_rxState);
    ldsSmartBaseResetStateMachine();
}

/**
 * @brief 初始化命令队列
 * @details 清除命令队列中的所有条目，将它们标记为非活动状态。
 */
static void ldsSmartBaseInitCmdQueue(void)
{
    rt_memset(&g_cmdQueue, 0, sizeof(g_cmdQueue));
}

/**
 * @brief 查找队列中最旧的待处理命令。
 * @details 遍历队列以查找具有最早发送时间戳的活动命令。
 *          此命令被视为重传的"头部"。
 * @return 指向最旧命令条目的指针，如果队列为空则返回RT_NULL。
 */
static lds_smart_base_cmd_queue_entry_t* ldsSmartBaseFindOldestCmd(void)
{
    lds_smart_base_cmd_queue_entry_t *oldest_cmd = RT_NULL;
    rt_tick_t min_timestamp = RT_TICK_MAX;

    for (int i = 0; i < SMART_BASE_CMD_QUEUE_SIZE; i++) {
        if (g_cmdQueue[i].active) {
            if (oldest_cmd == RT_NULL || (g_cmdQueue[i].sent_timestamp < min_timestamp)) {
                min_timestamp = g_cmdQueue[i].sent_timestamp;
                oldest_cmd = &g_cmdQueue[i];
            }
        }
    }
    return oldest_cmd;
}

/**
 * @brief 重传超时处理函数。
 * @details 当重传定时器到期时调用此函数。它处理队列中最旧命令的
 *          重传或丢弃。
 * @param parameter 未使用。
 */
static void ldsSmartBaseRetransmissionTimeout(void *parameter)
{
    uint8_t seq_bk = 0;
    rt_mutex_take(&g_smartBaseMutex, RT_WAITING_FOREVER);

    lds_smart_base_cmd_queue_entry_t *cmd_to_retry = ldsSmartBaseFindOldestCmd();

    if (cmd_to_retry != RT_NULL) {
        if (cmd_to_retry->retryCount >= SMART_BASE_RETRY_COUNT) {
            LOG_E("Max retries for cmd=0x%02X, seq=%d. Dropping.", cmd_to_retry->cmd, cmd_to_retry->seq);
            cmd_to_retry->active = false; // Drop the command
            g_errorCount ++;
            if(g_errorCount > SMART_BASE_MAX_ERROR_COUNT){
                LOG_W("SMART_BASE reach max error count,most likely not connected");
                g_enable = false;
                rt_timer_stop(&g_heartbeatTimer);
            }
        } else {
            cmd_to_retry->retryCount++;
            LOG_W("Retrying cmd 0x%04X, seq=%d, attempt %d/%d",
                  cmd_to_retry->cmd, cmd_to_retry->seq, cmd_to_retry->retryCount, SMART_BASE_RETRY_COUNT);

            // 使用相同的序列号重新发送命令
            seq_bk = g_currentSeq;
            g_currentSeq = cmd_to_retry->seq - 1; // ldsSmartBaseSendFrame会将其递增回来
            ldsSmartBaseSendFrame(cmd_to_retry->cmd, cmd_to_retry->data, cmd_to_retry->dataLen);
            cmd_to_retry->sent_timestamp = rt_tick_get();
            g_currentSeq = seq_bk;
        }
    }

    // 处理后，始终尝试为下一个待处理命令重启定时器
    ldsSmartBaseStartRetransmissionTimer();

    rt_mutex_release(&g_smartBaseMutex);
}

/**
 * @brief 如果有待处理命令，则启动重传定时器。
 * @details 查找最旧的命令并为其设置单次定时器。
 *          必须在持有互斥锁的情况下调用此函数。
 */
static void ldsSmartBaseStartRetransmissionTimer(void)
{
    rt_timer_stop(&g_retransmissionTimer);

    lds_smart_base_cmd_queue_entry_t *next_cmd = ldsSmartBaseFindOldestCmd();
    if (next_cmd != RT_NULL) {
        rt_tick_t timeout_tick = SMART_BASE_RESPONSE_TIMEOUT;
        // 可选：如果需要可以计算剩余时间，但固定超时更简单。
        rt_timer_control(&g_retransmissionTimer, RT_TIMER_CTRL_SET_TIME, &timeout_tick);
        rt_timer_start(&g_retransmissionTimer);
    }
}

/**
 * @brief 获取下一个序列号
 * @details 为发出的帧生成下一个序列号
 *
 * @return uint8_t 下一个序列号（0x00-0xFF）
 */
static uint8_t ldsSmartBaseGetNextSeq(void)
{
    return ++g_currentSeq;
}

/**
 * @brief 发送协议帧
 * @details 构造并发送带有CRC16的完整协议帧。
 *          注意：这是低级发送函数，不管理队列。
 *
 * @param cmd 命令码（大端序）
 * @param data 指向数据载荷的指针（如果dataLen为0可以为NULL）
 * @param dataLen 数据载荷长度
 * @return int 成功时返回0，失败时返回负错误码
 */
static int ldsSmartBaseSendFrame(uint8_t cmd, const uint8_t *data, uint16_t dataLen)
{
    uint8_t frame[SMART_BASE_MAX_FRAME_LEN];
    uint16_t frameLen;
    uint16_t crc16;
    rt_size_t written;
    uint8_t seq = 0;
    
    if(ldsAtGetFactoryTestMode()){
        return 0;
    }

    if(!g_enable){
        LOG_D("SMART_BASE not enabled");
        return -RT_ERROR;
    }

    seq = ldsSmartBaseGetNextSeq();

    if (g_smartBaseDev == RT_NULL) {
        LOG_E("SMART_BASE device not initialized");
        return -RT_ERROR;
    }

    if (dataLen > SMART_BASE_MAX_DATA_LEN) {
        LOG_E("Data length %d exceeds maximum %d", dataLen, SMART_BASE_MAX_DATA_LEN);
        return -RT_EINVAL;
    }

    /* 构造帧 */
    frameLen = 0;
    frame[frameLen++] = SMART_BASE_FRAME_HEAD1;          /* 帧头1 */
    frame[frameLen++] = SMART_BASE_FRAME_HEAD2;          /* 帧头2 */
    frame[frameLen++] = cmd;                             /* 命令 */
    frame[frameLen++] = seq;                             /* 序列号 */
    frame[frameLen++] = (uint8_t)(dataLen >> 8);         /* 长度高字节 */
    frame[frameLen++] = (uint8_t)(dataLen & 0xFF);       /* 长度低字节 */

    /* 复制数据载荷 */
    if (data != RT_NULL && dataLen > 0) {
        rt_memcpy(&frame[frameLen], data, dataLen);
        frameLen += dataLen;
    }

    /* 计算并追加CRC16 */
    crc16 = ldsUtilCheckCrc16(frame, frameLen);
    frame[frameLen++] = (uint8_t)(crc16 >> 8);       /* CRC16高字节 */
    frame[frameLen++] = (uint8_t)(crc16 & 0xFF);     /* CRC16低字节 */

    /* 发送帧 */
    written = rt_device_write(g_smartBaseDev, 0, frame, frameLen);
    if (written != frameLen) {
        LOG_E("Failed to send complete frame, sent %d of %d bytes", written, frameLen);
        return -RT_ERROR;
    }

    LOG_D("Sent frame:  cmd=0x%02X, seq=%d, len=%d", cmd, seq, dataLen);
    // LOG_HEX("smart_base-tx", 16, frame, frameLen);

    return seq; // 返回使用的序列号
}

/**
 * @brief 发送命令并将其添加到待处理队列。
 * @details 这是发送命令的新主函数。它在队列中查找空闲槽位，
 *          发送帧，并管理重传定时器。
 * @param cmd 命令码
 * @param data 指向数据载荷的指针
 * @param dataLen 数据载荷长度
 * @return 成功时返回0，失败时返回负错误码。
 */
static int ldsSmartBaseSendCommand(uint16_t cmd, const uint8_t *data, uint16_t dataLen)
{
    if(ldsAtGetFactoryTestMode()){
        return 0;
    }

    if(!g_enable){
        LOG_D("SMART_BASE not enabled");
        return -RT_ERROR;
    }

    rt_err_t result = rt_mutex_take(&g_smartBaseMutex, RT_WAITING_FOREVER);
    if (result != RT_EOK) {
        LOG_E("Failed to acquire mutex: %d", result);
        return -RT_ERROR;
    }

    int free_slot_idx = -1;
    for (int i = 0; i < SMART_BASE_CMD_QUEUE_SIZE; i++) {
        if (!g_cmdQueue[i].active) {
            free_slot_idx = i;
            break;
        }
    }

    if (free_slot_idx == -1) {
        LOG_E("Command queue is full. Cannot send cmd 0x%04X.", cmd);
        rt_mutex_release(&g_smartBaseMutex);
        return -RT_EBUSY;
    }

    // 备份当前序列号，因为ldsSmartBaseSendFrame会修改它。
    uint8_t seq_bak = g_currentSeq;
    int seq_sent = ldsSmartBaseSendFrame(cmd, data, dataLen);

    if (seq_sent < 0) {
        g_currentSeq = seq_bak; // 失败时恢复序列号
        rt_mutex_release(&g_smartBaseMutex);
        return seq_sent; // 传播错误
    }

    // 填充队列条目
    lds_smart_base_cmd_queue_entry_t *entry = &g_cmdQueue[free_slot_idx];
    entry->active = true;
    entry->seq = (uint8_t)seq_sent;
    entry->cmd = cmd;
    entry->dataLen = dataLen;
    if (dataLen > 0) {
        rt_memcpy(entry->data, data, dataLen);
    }
    entry->retryCount = 0;
    entry->sent_timestamp = rt_tick_get();

    LOG_D("Cmd 0x%04X with seq=%d added to queue.", cmd, entry->seq);

    // 如果定时器未运行（即队列为空），则启动它。
    rt_uint8_t timer_state = 0;
    rt_timer_control(&g_retransmissionTimer, RT_TIMER_CTRL_GET_STATE, &timer_state);
    if (timer_state == RT_TIMER_FLAG_DEACTIVATED) {
        ldsSmartBaseStartRetransmissionTimer();
    }

    rt_mutex_release(&g_smartBaseMutex);
    return 0;
}
static int ldsSmartBaseSendResponse(const lds_smart_base_frame_t *frame)
{
    int ret = 0;
    uint8_t seq_bk = 0;
    rt_mutex_take(&g_smartBaseMutex, RT_WAITING_FOREVER);
    seq_bk = g_currentSeq;
    g_currentSeq = frame->seq - 1; // ldsSmartBaseSendFrame会将其递增回来
    ret = ldsSmartBaseSendFrame(frame->cmd | SMART_BASE_RESPONSE_FLAG, frame->data, frame->dataLen);
    g_currentSeq = seq_bk;
    rt_mutex_release(&g_smartBaseMutex);
    return ret;
}
/**
 * @brief 处理接收到的协议帧
 * @details 处理完整的接收帧并根据命令类型进行分发
 *
 * @param frame 指向接收帧结构的指针
 * @return int 成功时返回0，失败时返回负错误码
 */
static int ldsSmartBaseProcessFrame(const lds_smart_base_frame_t *frame)
{
    if (frame == RT_NULL) {
        LOG_E("Invalid frame pointer");
        return -RT_EINVAL;
    }

    LOG_D("Processing frame: cmd=0x%02X, seq=%d, len=%d",
          frame->cmd, frame->seq, frame->dataLen);

    /* 在任何有效帧上重置心跳定时器 */
    rt_timer_stop(&g_heartbeatTimer);
    rt_timer_start(&g_heartbeatTimer);

    g_errorCount = 0;
    g_enable = true;
    // 这是一个ACK或响应帧。尝试将其与待处理命令匹配。
    rt_mutex_take(&g_smartBaseMutex, RT_WAITING_FOREVER);
    bool ack_matched = false;
    for (int i = 0; i < SMART_BASE_CMD_QUEUE_SIZE; i++) {
        if (g_cmdQueue[i].active && g_cmdQueue[i].seq == frame->seq) {
            LOG_D("ACK received for seq=%d. Removing from queue.", frame->seq);
            g_cmdQueue[i].active = false; // 停用命令
            ack_matched = true;

            // 命令已被确认。我们需要检查是否应该为下一个最旧的命令重启定时器。
            ldsSmartBaseStartRetransmissionTimer();
            break;
        }
    }
    if (!ack_matched) {
        LOG_D("Received ACK for unexpected seq=%d or command type 0x%02X", frame->seq, frame->cmd);
    }
    rt_mutex_release(&g_smartBaseMutex);


    switch (frame->cmd) {
        case LDS_SMART_BASE_CMD_KEY:
            LOG_D("Key command received: %d", frame->data[0]);
            ldsUacKeyCmdsend(frame->data[0] == 0x01);
            break;            
        case LDS_SMART_BASE_CMD_VERSION:
            if (frame->dataLen > 0) {
                int max_len = frame->dataLen > SMART_BASE_VERSION_MAX_LEN - 1 ? SMART_BASE_VERSION_MAX_LEN - 1 : frame->dataLen;
                LOG_I("Version info received: %.*s", frame->dataLen, frame->data);
                rt_memcpy(g_smart_base_version, frame->data, max_len);
                g_smart_base_version[max_len] = '\0';
            }
            break;

        case LDS_SMART_BASE_CMD_STATUS:
            if(frame->dataLen != 2){
                LOG_E("STATUS command data len %d error", frame->dataLen);
                return -RT_ERROR;
            }
            g_smart_base_status_connect = frame->data[0] == 0x01;
            if(g_smart_base_status_active != frame->data[1]){
                g_smart_base_status_active = frame->data[1];
                ldsSmartBaseSendSelectCmd(g_smart_base_status_active == 1);
            }
            break;
        default:
            LOG_W("Unknown command received: 0x%04X", frame->cmd);
            return -RT_ERROR;
    }
    ldsSmartBaseSendResponse(frame);
    return 0;
}

/**
 * @brief 启动解析超时定时器
 * @details 启动或重启解析超时定时器以防止状态机挂起
 */
static void ldsSmartBaseStartParseTimer(void)
{
    rt_timer_stop(&g_parseTimer);
    rt_timer_start(&g_parseTimer);
}

/**
 * @brief 重置帧解析状态机
 * @details 将状态机重置为空闲状态并清除缓冲区
 */
static void ldsSmartBaseResetStateMachine(void)
{
    /* 停止解析超时定时器 */
    rt_timer_stop(&g_parseTimer);

    g_rxState = SMART_BASE_STATE_IDLE;
    g_rxIndex = 0;
    rt_memset(&g_rxFrame, 0, sizeof(g_rxFrame));
    rt_memset(g_rxBuffer, 0, sizeof(g_rxBuffer));
}

/**
 * @brief 协议帧解析状态机
 * @details 根据协议规范解析传入的字节
 *
 * @param data 指向接收数据缓冲区的指针
 * @param size 接收数据的大小
 * @return int 成功时返回0，失败时返回负错误码
 */
static int ldsSmartBaseParseData(const uint8_t *data, rt_size_t size)
{
    if (data == RT_NULL || size == 0) {
        LOG_E("Invalid data or size");
        return -RT_EINVAL;
    }

    for (rt_size_t i = 0; i < size; i++) {
        uint8_t byte = data[i];
        // LOG_D("state %d", g_rxState);
        switch (g_rxState) {
            case SMART_BASE_STATE_IDLE:
                if (byte == SMART_BASE_FRAME_HEAD1) {
                    ldsSmartBaseResetStateMachine();
                    g_rxBuffer[g_rxIndex++] = byte;
                    g_rxFrame.head1 = byte;
                    g_rxState = SMART_BASE_STATE_HEAD_1;
                    /* 进入解析状态时启动解析超时定时器 */
                    ldsSmartBaseStartParseTimer();
                }
                break;

            case SMART_BASE_STATE_HEAD_1:
                if (byte == SMART_BASE_FRAME_HEAD2) {
                    g_rxBuffer[g_rxIndex++] = byte;
                    g_rxFrame.head2 = byte;
                    g_rxState = SMART_BASE_STATE_CMD;
                    /* 进入解析状态时启动解析超时定时器 */
                    ldsSmartBaseStartParseTimer();
                }
                break;

            case SMART_BASE_STATE_CMD:
                g_rxBuffer[g_rxIndex++] = byte;
                g_rxFrame.cmd = byte;
                g_rxState = SMART_BASE_STATE_SEQ;
                ldsSmartBaseStartParseTimer();
                break;

            case SMART_BASE_STATE_SEQ:
                g_rxBuffer[g_rxIndex++] = byte;
                g_rxFrame.seq = byte;
                g_rxState = SMART_BASE_STATE_LEN_H;
                ldsSmartBaseStartParseTimer();
                break;

            case SMART_BASE_STATE_LEN_H:
                g_rxBuffer[g_rxIndex++] = byte;
                g_rxFrame.dataLen = (uint16_t)(byte << 8);
                g_rxState = SMART_BASE_STATE_LEN_L;
                ldsSmartBaseStartParseTimer();
                break;

            case SMART_BASE_STATE_LEN_L:
                g_rxBuffer[g_rxIndex++] = byte;
                g_rxFrame.dataLen |= byte;

                /* 验证数据长度 */
                if (g_rxFrame.dataLen > SMART_BASE_MAX_DATA_LEN) {
                    LOG_E("Invalid data length: %d", g_rxFrame.dataLen);
                    ldsSmartBaseResetStateMachine();
                    break;
                }

                if (g_rxFrame.dataLen == 0) {
                    g_rxState = SMART_BASE_STATE_CRC16_H;
                } else {
                    g_rxState = SMART_BASE_STATE_DATA;
                }
                rt_memset(g_rxFrame.data, 0, sizeof(g_rxFrame.data));
                ldsSmartBaseStartParseTimer();
                break;

            case SMART_BASE_STATE_DATA:
                g_rxBuffer[g_rxIndex++] = byte;
                g_rxFrame.data[g_rxIndex - 7] = byte;  /* 数据从索引6开始 */

                if (g_rxIndex >= (6 + g_rxFrame.dataLen)) {
                    g_rxState = SMART_BASE_STATE_CRC16_H;
                }
                ldsSmartBaseStartParseTimer();
                break;

            case SMART_BASE_STATE_CRC16_H:
                g_rxBuffer[g_rxIndex++] = byte;
                g_rxFrame.crc16 = (uint16_t)(byte << 8);
                g_rxState = SMART_BASE_STATE_CRC16_L;
                ldsSmartBaseStartParseTimer();
                break;

            case SMART_BASE_STATE_CRC16_L:
                g_rxBuffer[g_rxIndex++] = byte;
                g_rxFrame.crc16 |= byte;

                /* 验证CRC16 */
                uint16_t calculatedCrc16 = ldsUtilCheckCrc16(g_rxBuffer, g_rxIndex - 2);
                if (calculatedCrc16 != g_rxFrame.crc16) {
                    LOG_E("CRC16 mismatch: calculated=0x%04X, received=0x%04X",
                          calculatedCrc16, g_rxFrame.crc16);
                    LOG_HEX("smart_base-rx-err", 16, g_rxBuffer, g_rxIndex);
                    ldsSmartBaseResetStateMachine();
                    break;
                }

                // LOG_HEX("smart_base-rx", 16, g_rxBuffer, g_rxIndex);
                ldsSmartBaseProcessFrame(&g_rxFrame);
                ldsSmartBaseResetStateMachine();
                break;

            default:
                LOG_E("Invalid state: %d", g_rxState);
                ldsSmartBaseResetStateMachine();
                break;
        }

        /* 防止缓冲区溢出 */
        if (g_rxIndex >= SMART_BASE_MAX_FRAME_LEN) {
            LOG_E("Frame buffer overflow");
            ldsSmartBaseResetStateMachine();
            break;
        }
    }

    return 0;
}

/**
 * @brief UART数据处理回调函数
 * @details 向UART驱动程序注册的用于数据处理的回调函数
 *
 * @param dev RT-Thread设备句柄
 * @param data 指向接收数据缓冲区的指针
 * @param size 接收数据的字节大小
 * @return int 成功时返回0，失败时返回负错误码
 */
int ldsSmartBaseProcess(rt_device_t dev, const uint8_t *data, rt_size_t size)
{
    if (data == RT_NULL || size == 0) {
        LOG_E("Invalid data or size");
        return -RT_EINVAL;
    }

    if (dev == RT_NULL) {
        LOG_E("Invalid device handle");
        return -RT_EINVAL;
    }

    LOG_D("Received %d bytes from %s", size, dev->parent.name);

    if(ldsAtGetFactoryTestMode()){
        rt_device_write(g_smartBaseDev, 0, data, size);
        return 0;
    }
    // 这里没有互斥锁，解析在UART上下文中进行。
    // 互斥锁在处理/确认逻辑内部使用。
    int ret = ldsSmartBaseParseData(data, size);

    return ret;
}

/* ================================ 公共API函数 =================== */

int ldsSmartBaseSetSelectMode(int8_t mode)
{
    if(g_smart_base_select_mode != mode){
        g_smart_base_select_mode = mode;
        ldsSmartBaseSendSelectCmd(g_smart_base_status_active == 1);
    }
    return 0;
}

uint8_t ldsSmartBaseGetStatus(void)
{
    return g_smart_base_status_connect;
}

int ldsSmartBaseGetPowerCtrl(void)
{
    return rt_pin_read(g_smartBasePowerCtrl);
}

int ldsSmartBaseSetPowerCtrl(bool on)
{
    if (g_smartBasePowerCtrl <= 0) {
        LOG_E("SMART_BASE power control pin not initialized");
        return -RT_ERROR;
    }
    rt_pin_write(g_smartBasePowerCtrl, on ? PIN_HIGH : PIN_LOW);
    g_enable = on;
    if(!on){
        g_errorCount = SMART_BASE_MAX_ERROR_COUNT;
        rt_timer_stop(&g_heartbeatTimer);
    } else {
        g_errorCount = 0;
        rt_timer_stop(&g_heartbeatTimer);
        rt_timer_start(&g_heartbeatTimer);
    }
    return 0;
}

const char *ldsSmartBaseGetVersion(void)
{
    return g_smart_base_version;
}

int ldsSmartBaseQueryVersion(void)
{
    return ldsSmartBaseSendCommand(LDS_SMART_BASE_CMD_VERSION, RT_NULL, 0);
}

int ldsSmartBaseQueryStatus(void)
{
    return ldsSmartBaseSendCommand(LDS_SMART_BASE_CMD_STATUS, RT_NULL, 0);
}

/**
 * @brief 初始化智能底座通信系统
 * @details 初始化硬件、UART通信、定时器和状态机
 *
 * @return int 成功时返回0，失败时返回负错误码
 *
 * @note 此函数执行完整的智能底座系统初始化，包括：
 *       - 电源控制引脚设置
 *       - 带回调的UART接口初始化
 *       - 心跳和重传的定时器配置
 *       - 线程安全的互斥锁初始化
 *       - 状态机和命令队列初始化
 */
int ldsSmartBaseInit(void)
{
    rt_err_t result;

    /* 为线程安全初始化互斥锁 */
    result = rt_mutex_init(&g_smartBaseMutex, "smart_base_mutex", RT_IPC_FLAG_PRIO);
    if (result != RT_EOK) {
        LOG_E("Failed to initialize mutex: %d", result);
        return -RT_ERROR;
    }

    /* 初始化电源控制引脚 */
    g_smartBasePowerCtrl = power_ctrl_pin_init(SMART_BASE_POWER_CTRL_PIN, PIN_HIGH);
    if (g_smartBasePowerCtrl < 0) {
        LOG_E("Failed to initialize SMART_BASE power control pin %s", SMART_BASE_POWER_CTRL_PIN);
        rt_mutex_detach(&g_smartBaseMutex);
        return -RT_ERROR;
    }

    /* 使用回调初始化UART */
    g_smartBaseDev = ldsUartInit(SMART_BASE_SERIAL_NAME, LDS_UART_INDEX_5, ldsSmartBaseProcess);
    if (g_smartBaseDev == RT_NULL) {
        LOG_E("Failed to initialize SMART_BASE UART %s", SMART_BASE_SERIAL_NAME);
        rt_mutex_detach(&g_smartBaseMutex);
        return -RT_ERROR;
    }

    /* 初始化心跳定时器 */
    rt_timer_init(&g_heartbeatTimer, "base_hb",
                  ldsSmartBaseHeartbeatTimeout,
                  RT_NULL,
                  SMART_BASE_HEARTBEAT_TIMEOUT,
                  RT_TIMER_FLAG_SOFT_TIMER | RT_TIMER_FLAG_PERIODIC);
    rt_timer_start(&g_heartbeatTimer);

    /* 初始化重传定时器 */
    rt_timer_init(&g_retransmissionTimer, "base_retry",
                  ldsSmartBaseRetransmissionTimeout,
                  RT_NULL,
                  SMART_BASE_RESPONSE_TIMEOUT,
                  RT_TIMER_FLAG_SOFT_TIMER | RT_TIMER_FLAG_ONE_SHOT);

    /* 初始化解析超时定时器 */
    rt_timer_init(&g_parseTimer, "base_parse",
                  ldsSmartBaseParseTimeout,
                  RT_NULL,
                  SMART_BASE_PARSE_TIMEOUT,
                  RT_TIMER_FLAG_SOFT_TIMER | RT_TIMER_FLAG_ONE_SHOT);

    /* 初始化状态机 */
    ldsSmartBaseResetStateMachine();
    g_currentSeq = 0;

    /* 初始化命令队列 */
    ldsSmartBaseInitCmdQueue();


    LOG_I("Smart base communication system initialized successfully");
    return 0;
}

/**
 * @brief 反初始化智能底座通信系统
 * @details 清理所有资源并停止通信
 *
 * @return int 成功时返回0，失败时返回负错误码
 */
int ldsSmartBaseDeinit(void)
{
    /* 停止定时器 */
    rt_timer_stop(&g_heartbeatTimer);
    rt_timer_stop(&g_retransmissionTimer);
    rt_timer_stop(&g_parseTimer);
    rt_timer_detach(&g_heartbeatTimer);
    rt_timer_detach(&g_retransmissionTimer);
    rt_timer_detach(&g_parseTimer);

    /* 重置状态机 */
    ldsSmartBaseResetStateMachine();

    /* 清除命令队列 */
    rt_mutex_take(&g_smartBaseMutex, RT_WAITING_FOREVER);
    ldsSmartBaseInitCmdQueue();
    rt_mutex_release(&g_smartBaseMutex);

    /* 关闭UART设备 */
    if (g_smartBaseDev != RT_NULL) {
        rt_device_close(g_smartBaseDev);
        g_smartBaseDev = RT_NULL;
    }

    /* 关闭设备电源 */
    if (g_smartBasePowerCtrl > 0) {
        rt_pin_write(g_smartBasePowerCtrl, PIN_LOW);
        g_smartBasePowerCtrl = -1;
    }

    /* 清理互斥锁 */
    rt_mutex_detach(&g_smartBaseMutex);

    LOG_I("Smart base communication system deinitialized");
    return 0;
}

/* ================================ MSH调试命令 ===================== */
#ifdef RT_USING_FINSH

static int ldsSmartBaseQueueStatus(void)
{
    rt_kprintf("Command Queue Status (Size: %d):\n", SMART_BASE_CMD_QUEUE_SIZE);
    bool empty = true;
    for (int i = 0; i < SMART_BASE_CMD_QUEUE_SIZE; i++) {
        if (g_cmdQueue[i].active) {
            empty = false;
            rt_kprintf("  Slot %d: [ACTIVE]\n", i);
            rt_kprintf("    seq: %d, cmd: 0x%04X,\n",
                       g_cmdQueue[i].seq, g_cmdQueue[i].cmd);
            rt_kprintf("    retries: %d, sent_at: %u\n",
                       g_cmdQueue[i].retryCount, g_cmdQueue[i].sent_timestamp);
        }
    }
    if (empty) {
        rt_kprintf("  Queue is empty.\n");
    }
    return 0;
}


/**
 * @brief 智能底座操作的MSH命令
 * @details 为测试智能底座通信提供命令行接口
 *
 * @param argc 参数计数
 * @param argv 参数向量
 * @return int 成功时返回0，失败时返回负错误码
 */
static int ldsSmartBaseCmd(int argc, char **argv)
{
    if (argc < 2) {
        rt_kprintf("Usage: smart_base <command> [args...]\n");
        rt_kprintf("Commands:\n");
        rt_kprintf("  init                    - Initialize smart base system\n");
        rt_kprintf("  deinit                  - Deinitialize smart base system\n");
        rt_kprintf("  reset                   - Reset smart base device\n");
        rt_kprintf("  version                 - Query version information\n");
        rt_kprintf("  status                  - Show system status and queue\n");
        rt_kprintf("  test_crc16              - Test CRC16 implementation\n");
        return 0;
    }

    if (rt_strcmp(argv[1], "init") == 0) {
        int ret = ldsSmartBaseInit();
        rt_kprintf("Smart base init %s\n", ret == 0 ? "success" : "failed");
        return ret;
    }

    if (rt_strcmp(argv[1], "deinit") == 0) {
        int ret = ldsSmartBaseDeinit();
        rt_kprintf("Smart base deinit %s\n", ret == 0 ? "success" : "failed");
        return ret;
    }

    if (rt_strcmp(argv[1], "reset") == 0) {
        ldsSmartBaseReset();
        rt_kprintf("Smart base device reset\n");
        return 0;
    }

    if (rt_strcmp(argv[1], "version") == 0) {
        int ret = ldsSmartBaseQueryVersion();
        rt_kprintf("Version query %s\n", ret == 0 ? "sent" : "failed");
        return ret;
    }


    if (rt_strcmp(argv[1], "status") == 0) {
        rt_mutex_take(&g_smartBaseMutex, RT_WAITING_FOREVER);
        rt_kprintf("Smart base System Status:\n");
        rt_kprintf("  Device: %s\n", g_smartBaseDev ? "initialized" : "not initialized");
        rt_kprintf("  Power Control: %s\n", g_smartBasePowerCtrl > 0 ? "enabled" : "disabled");
        rt_kprintf("  Current Sequence: %d\n", g_currentSeq);
        rt_kprintf("  RX State: %d\n", g_rxState);
        rt_kprintf("  RX Index: %d\n", g_rxIndex);

        /* 检查解析定时器状态 */
        rt_uint8_t parse_timer_state = 0;
        rt_timer_control(&g_parseTimer, RT_TIMER_CTRL_GET_STATE, &parse_timer_state);
        rt_kprintf("  Parse Timer: %s\n", parse_timer_state == RT_TIMER_FLAG_ACTIVATED ? "active" : "inactive");

        ldsSmartBaseQueueStatus();
        rt_mutex_release(&g_smartBaseMutex);
        return 0;
    }

    rt_kprintf("Unknown command: %s\n", argv[1]);
    return -RT_EINVAL;
}

MSH_CMD_EXPORT_ALIAS(ldsSmartBaseCmd, smart_base, Smart base communication protocol commands);
#endif