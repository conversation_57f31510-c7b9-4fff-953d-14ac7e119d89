/**
 * @file lds_third_party.c
 * @brief LDS第三方通信协议栈实现
 * @details 此文件实现了第三方设备的完整通信协议栈
 *          遵循指定的大端字节序协议格式。
 *          此版本包含命令队列，用于强健的多命令处理。
 * <AUTHOR> Team
 * @version 1.0
 * @date 2025-07-23
 *
 * @copyright Copyright (c) 2025 LDS
 *
 * 协议格式:
 * Head(2) + CMD(1) + SEQ(1) + LEN(2) + DATA(可变) + CRC16(2)
 * - 大端字节序（MSB在前）
 * - Head: 所有帧固定为0x5AA5
 * - SEQ: 0x00-0xFF序列号，用于请求/响应匹配
 * - CMD: 定义操作的功能码
 * - LEN: DATA字段的字节长度
 * - DATA: 可变长度载荷数据
 * - CRC16: 16位CRC校验和（从Head到CRC16-1所有字节的CRC16）
 */

#include <rtdevice.h>
#include <stdlib.h>
#include <string.h>
#include "lds_utils.h"
#include "lds_uart.h"
#include "lds_third_party.h"
#include "lds_led_config.h"
#include "lds_uac.h"
#include "lds_mic.h"
#include "lds_key.h"
#include "lds_smart_base.h"
#include "lds_at.h"

#define DBG_TAG "THIRD_PARTY"
// #define DBG_LVL DBG_INFO
#include <rtdbg.h>

/* ================================ Configuration ========================== */
#define THIRD_PARTY_SERIAL_NAME         "uart7"
#define THIRD_PARTY_POWER_CTRL_USB      "PD.0"
#define THIRD_PARTY_CMD_QUEUE_SIZE      16                           /**< 命令队列大小，支持最多16个待处理命令 */
#define THIRD_PARTY_MAX_ERROR_COUNT     10                           /**< 假定设备未连接前的最大连续错误数 */
#define THIRD_PARTY_RESPONSE_FLAG       0x80
/* ================================ 超时配置 ================== */
#define THIRD_PARTY_RESPONSE_TIMEOUT    (RT_TICK_PER_SECOND * 2)    /**< 2秒响应超时 */
#define THIRD_PARTY_PARSE_TIMEOUT       (RT_TICK_PER_SECOND * 1)   /**< 状态机1秒解析超时 */
#define THIRD_PARTY_RETRY_COUNT         2                           /**< 最大重试次数 */

/* ================================ 全局变量 ======================= */
static rt_device_t g_thirdPartyDev = RT_NULL;       /**< UART设备句柄 */
static rt_base_t g_powerCtrlUsb = -1;            /* usb电源控制引脚 */
static struct rt_timer g_retransmissionTimer; /**< 命令队列重传定时器 */
static struct rt_timer g_parseTimer;        /**< 状态机解析超时定时器 */
static struct rt_mutex g_thirdPartyMutex;          /**< 线程安全互斥锁 */
static uint8_t g_errorCount = 0 ;       /**< 连续错误计数 */

/* ================================ 协议状态机 ================ */
static lds_third_party_frame_t g_rxFrame;           /**< 当前接收帧 */
static uint8_t g_rxBuffer[THIRD_PARTY_MAX_FRAME_LEN]; /**< 帧接收缓冲区 */
static uint16_t g_rxIndex = 0;              /**< 当前接收索引 */
static uint8_t g_currentSeq = 0;            /**< 当前序列号 */

/* ================================ 命令队列管理 ================ */
/**
 * @brief 待处理命令队列条目结构体
 * @details 包含等待ACK的命令的所有信息，包括重试管理。
 */
typedef struct {
    bool active;                            /**< 定义此队列槽是否在使用 */
    uint8_t seq;                            /**< 此命令的唯一序列号 */
    uint8_t cmd;                           /**< 命令码 */
    uint16_t dataLen;                       /**< 数据载荷长度 */
    uint8_t data[THIRD_PARTY_MAX_DATA_LEN];         /**< 数据载荷 */
    uint8_t retryCount;                     /**< 当前重试计数 */
    rt_tick_t sent_timestamp;               /**< 命令最后发送时的系统时钟 */
} lds_third_party_cmd_queue_entry_t;

static lds_third_party_cmd_queue_entry_t g_cmdQueue[THIRD_PARTY_CMD_QUEUE_SIZE]; /**< 命令队列 */
static void ldsThirdPartyStartRetransmissionTimer(void);
static int ldsThirdPartySendFrame(uint8_t cmd, const uint8_t *data, uint16_t dataLen);
static void ldsThirdPartyResetStateMachine(void);

/**
 * @brief 第三方协议状态枚举
 * @details 定义协议帧解析状态机的状态
 */
typedef enum {
    THIRD_PARTY_STATE_IDLE = 0,        /**< 等待帧头 */
    THIRD_PARTY_STATE_HEAD_1,          /**< 接收帧头1 0x5a */
    THIRD_PARTY_STATE_HEAD_2,          /**< 接收帧头2 0xa5 */
    THIRD_PARTY_STATE_CMD,           /**< 接收命令字节 */
    THIRD_PARTY_STATE_SEQ,             /**< 接收序列号 */
    THIRD_PARTY_STATE_LEN_H,           /**< 接收长度高字节 */
    THIRD_PARTY_STATE_LEN_L,           /**< 接收长度低字节 */
    THIRD_PARTY_STATE_DATA,            /**< 接收数据载荷 */
    THIRD_PARTY_STATE_CRC16_H,         /**< 接收CRC16高字节 */
    THIRD_PARTY_STATE_CRC16_L,         /**< 接收CRC16低字节 */
} LDS_THIRD_PARTY_STATE_E;

static LDS_THIRD_PARTY_STATE_E g_rxState = THIRD_PARTY_STATE_IDLE;

/**
 * @brief 解析超时处理函数
 * @details 当解析超时发生时调用，重置状态机以防止挂起
 *
 * @param parameter 定时器参数（未使用）
 */
static void ldsThirdPartyParseTimeout(void *parameter)
{
    LOG_W("Parse timeout state %d, reset", g_rxState);
    ldsThirdPartyResetStateMachine();
}

/**
 * @brief 初始化命令队列
 * @details 清除命令队列中的所有条目，将它们标记为非活动状态。
 */
static void ldsThirdPartyInitCmdQueue(void)
{
    rt_memset(&g_cmdQueue, 0, sizeof(g_cmdQueue));
}

/**
 * @brief 查找队列中最旧的待处理命令。
 * @details 遍历队列以查找具有最早发送时间戳的活动命令。
 *          此命令被视为重传的"头部"。
 * @return 指向最旧命令条目的指针，如果队列为空则返回RT_NULL。
 */
static lds_third_party_cmd_queue_entry_t* ldsThirdPartyFindOldestCmd(void)
{
    lds_third_party_cmd_queue_entry_t *oldest_cmd = RT_NULL;
    rt_tick_t min_timestamp = RT_TICK_MAX;

    for (int i = 0; i < THIRD_PARTY_CMD_QUEUE_SIZE; i++) {
        if (g_cmdQueue[i].active) {
            if (oldest_cmd == RT_NULL || (g_cmdQueue[i].sent_timestamp < min_timestamp)) {
                min_timestamp = g_cmdQueue[i].sent_timestamp;
                oldest_cmd = &g_cmdQueue[i];
            }
        }
    }
    return oldest_cmd;
}

/**
 * @brief 重传超时处理函数。
 * @details 当重传定时器到期时调用此函数。它处理队列中最旧命令的
 *          重传或丢弃。
 * @param parameter 未使用。
 */
static void ldsThirdPartyRetransmissionTimeout(void *parameter)
{
    uint8_t seq_bk = 0;
    rt_mutex_take(&g_thirdPartyMutex, RT_WAITING_FOREVER);

    lds_third_party_cmd_queue_entry_t *cmd_to_retry = ldsThirdPartyFindOldestCmd();

    if (cmd_to_retry != RT_NULL) {
        if (cmd_to_retry->retryCount >= THIRD_PARTY_RETRY_COUNT) {
            LOG_E("Max retries for cmd=0x%02X, seq=%d. Dropping.", cmd_to_retry->cmd, cmd_to_retry->seq);
            cmd_to_retry->active = false; // Drop the command
            g_errorCount ++;
            if(g_errorCount > THIRD_PARTY_MAX_ERROR_COUNT){
                LOG_D("THIRD_PARTY reach max error count,most likely not connected");
            }
        } else {
            cmd_to_retry->retryCount++;
            LOG_W("Retrying cmd 0x%04X, seq=%d, attempt %d/%d",
                  cmd_to_retry->cmd, cmd_to_retry->seq, cmd_to_retry->retryCount, THIRD_PARTY_RETRY_COUNT);

            // 使用相同的序列号重新发送命令
            seq_bk = g_currentSeq;
            g_currentSeq = cmd_to_retry->seq - 1; // ldsThirdPartySendFrame会将其递增回来
            ldsThirdPartySendFrame(cmd_to_retry->cmd, cmd_to_retry->data, cmd_to_retry->dataLen);
            cmd_to_retry->sent_timestamp = rt_tick_get();
            g_currentSeq = seq_bk;
        }
    }

    // 处理后，始终尝试为下一个待处理命令重启定时器
    ldsThirdPartyStartRetransmissionTimer();

    rt_mutex_release(&g_thirdPartyMutex);
}

/**
 * @brief 如果有待处理命令，则启动重传定时器。
 * @details 查找最旧的命令并为其设置单次定时器。
 *          必须在持有互斥锁的情况下调用此函数。
 */
static void ldsThirdPartyStartRetransmissionTimer(void)
{
    rt_timer_stop(&g_retransmissionTimer);

    lds_third_party_cmd_queue_entry_t *next_cmd = ldsThirdPartyFindOldestCmd();
    if (next_cmd != RT_NULL) {
        rt_tick_t timeout_tick = THIRD_PARTY_RESPONSE_TIMEOUT;
        // 可选：如果需要可以计算剩余时间，但固定超时更简单。
        rt_timer_control(&g_retransmissionTimer, RT_TIMER_CTRL_SET_TIME, &timeout_tick);
        rt_timer_start(&g_retransmissionTimer);
    }
}

/**
 * @brief 获取下一个序列号
 * @details 为发出的帧生成下一个序列号
 *
 * @return uint8_t 下一个序列号（0x00-0xFF）
 */
static uint8_t ldsThirdPartyGetNextSeq(void)
{
    return ++g_currentSeq;
}

/**
 * @brief 发送协议帧
 * @details 构造并发送带有CRC16的完整协议帧。
 *          注意：这是低级发送函数，不管理队列。
 *
 * @param cmd 命令码（大端序）
 * @param data 指向数据载荷的指针（如果dataLen为0可以为NULL）
 * @param dataLen 数据载荷长度
 * @return int 成功时返回0，失败时返回负错误码
 */
static int ldsThirdPartySendFrame(uint8_t cmd, const uint8_t *data, uint16_t dataLen)
{
    uint8_t frame[THIRD_PARTY_MAX_FRAME_LEN];
    uint16_t frameLen;
    uint16_t crc16;
    rt_size_t written;
    uint8_t seq = 0;

    if(ldsAtGetFactoryTestMode()){
        return 0;
    }

    seq = ldsThirdPartyGetNextSeq();

    if (g_thirdPartyDev == RT_NULL) {
        LOG_E("THIRD_PARTY device not initialized");
        return -RT_ERROR;
    }

    if (dataLen > THIRD_PARTY_MAX_DATA_LEN) {
        LOG_E("Data length %d exceeds maximum %d", dataLen, THIRD_PARTY_MAX_DATA_LEN);
        return -RT_EINVAL;
    }

    /* 构造帧 */
    frameLen = 0;
    frame[frameLen++] = THIRD_PARTY_FRAME_HEAD1;          /* 帧头1 */
    frame[frameLen++] = THIRD_PARTY_FRAME_HEAD2;          /* 帧头2 */
    frame[frameLen++] = cmd;                             /* 命令 */
    frame[frameLen++] = seq;                             /* 序列号 */
    frame[frameLen++] = (uint8_t)(dataLen >> 8);         /* 长度高字节 */
    frame[frameLen++] = (uint8_t)(dataLen & 0xFF);       /* 长度低字节 */

    /* 复制数据载荷 */
    if (data != RT_NULL && dataLen > 0) {
        rt_memcpy(&frame[frameLen], data, dataLen);
        frameLen += dataLen;
    }

    /* 计算并追加CRC16 */
    crc16 = ldsUtilCheckCrc16(frame, frameLen);
    frame[frameLen++] = (uint8_t)(crc16 >> 8);       /* CRC16高字节 */
    frame[frameLen++] = (uint8_t)(crc16 & 0xFF);     /* CRC16低字节 */

    /* 发送帧 */
    written = rt_device_write(g_thirdPartyDev, 0, frame, frameLen);
    if (written != frameLen) {
        LOG_E("Failed to send complete frame, sent %d of %d bytes", written, frameLen);
        return -RT_ERROR;
    }

    LOG_D("Sent frame:  cmd=0x%02X, seq=%d, len=%d", cmd, seq, dataLen);
    // LOG_HEX("third_party-tx", 16, frame, frameLen);

    return seq; // 返回使用的序列号
}

/**
 * @brief 发送命令并将其添加到待处理队列。
 * @details 这是发送命令的新主函数。它在队列中查找空闲槽位，
 *          发送帧，并管理重传定时器。
 * @param cmd 命令码
 * @param data 指向数据载荷的指针
 * @param dataLen 数据载荷长度
 * @return 成功时返回0，失败时返回负错误码。
 */
int ldsThirdPartySendCommand(uint16_t cmd, const uint8_t *data, uint16_t dataLen)
{
    if(ldsAtGetFactoryTestMode()){
        return 0;
    }
    
    rt_err_t result = rt_mutex_take(&g_thirdPartyMutex, RT_WAITING_FOREVER);
    if (result != RT_EOK) {
        LOG_E("Failed to acquire mutex: %d", result);
        return -RT_ERROR;
    }

    int free_slot_idx = -1;
    for (int i = 0; i < THIRD_PARTY_CMD_QUEUE_SIZE; i++) {
        if (!g_cmdQueue[i].active) {
            free_slot_idx = i;
            break;
        }
    }

    if (free_slot_idx == -1) {
        LOG_E("Command queue is full. Cannot send cmd 0x%04X.", cmd);
        rt_mutex_release(&g_thirdPartyMutex);
        return -RT_EBUSY;
    }

    // 备份当前序列号，因为ldsThirdPartySendFrame会修改它。
    uint8_t seq_bak = g_currentSeq;
    int seq_sent = ldsThirdPartySendFrame(cmd, data, dataLen);

    if (seq_sent < 0) {
        g_currentSeq = seq_bak; // 失败时恢复序列号
        rt_mutex_release(&g_thirdPartyMutex);
        return seq_sent; // 传播错误
    }

    // 填充队列条目
    lds_third_party_cmd_queue_entry_t *entry = &g_cmdQueue[free_slot_idx];
    entry->active = true;
    entry->seq = (uint8_t)seq_sent;
    entry->cmd = cmd;
    entry->dataLen = dataLen;
    if (dataLen > 0) {
        rt_memcpy(entry->data, data, dataLen);
    }
    entry->retryCount = 0;
    entry->sent_timestamp = rt_tick_get();

    LOG_D("Cmd 0x%04X with seq=%d added to queue.", cmd, entry->seq);

    // 如果定时器未运行（即队列为空），则启动它。
    rt_uint8_t timer_state = 0;
    rt_timer_control(&g_retransmissionTimer, RT_TIMER_CTRL_GET_STATE, &timer_state);
    if (timer_state == RT_TIMER_FLAG_DEACTIVATED) {
        ldsThirdPartyStartRetransmissionTimer();
    }

    rt_mutex_release(&g_thirdPartyMutex);
    return 0;
}
static int ldsThirdPartySendResponse(lds_third_party_frame_t *frame, int ack)
{
    int ret = 0;
    uint8_t seq_bk = 0;

    rt_mutex_take(&g_thirdPartyMutex, RT_WAITING_FOREVER);
    frame->dataLen += 1;
    if(frame->dataLen > THIRD_PARTY_MAX_DATA_LEN){
        LOG_E("command data len %d error", frame->dataLen);
        frame->dataLen -= 1;
    }
    frame->data[frame->dataLen - 1] = ack;
    seq_bk = g_currentSeq;
    g_currentSeq = frame->seq - 1; // ldsThirdPartySendFrame会将其递增回来
    ret = ldsThirdPartySendFrame(frame->cmd | THIRD_PARTY_RESPONSE_FLAG, frame->data, frame->dataLen);
    g_currentSeq = seq_bk;
    rt_mutex_release(&g_thirdPartyMutex);
    return ret;
}
static int ldsThirdPartySendMuteCmd(bool main, int mute)
{
    int ret = 0;
    ret = ldsMicSetMuteControl(main ? LDS_MIC_ADDR_HOST : LDS_MIC_ADDR_SLAVE_BROADCAST, mute);
    if(ret){
        LOG_E("Send mute cmd failed");
        return ret;
    }
    if(mute != LDS_MIC_MUTE_ON){
        ret = ldsLedOff(ldsLedGetConfigPin(LED_MAIN_MUTE));
    } else {
        ret = ldsLedOn(ldsLedGetConfigPin(LED_MAIN_MUTE));
    }
    return ret;
}
static int ldsThirdPartySendEffectCmd(int effect)
{
    int ret = 0;
    ret = ldsMicSetSoundMode(effect);
    if(ret){
        LOG_E("Send effect cmd failed");
        return ret;
    }
    if(effect == LDS_MIC_SOUND_MODE_STANDARD){
        ret = ldsLedOn(ldsLedGetConfigPin(LED_STD));
        ret |= ldsLedOff(ldsLedGetConfigPin(LED_BOY));
        ret |= ldsLedOff(ldsLedGetConfigPin(LED_GIRL));
    }
    else if(effect == LDS_MIC_SOUND_MODE_FEMALE){
        ret = ldsLedOn(ldsLedGetConfigPin(LED_GIRL));
        ret |= ldsLedOff(ldsLedGetConfigPin(LED_BOY));
        ret |= ldsLedOff(ldsLedGetConfigPin(LED_STD));
    }
    else if(effect == LDS_MIC_SOUND_MODE_MALE){
        ret = ldsLedOn(ldsLedGetConfigPin(LED_BOY));
        ret |= ldsLedOff(ldsLedGetConfigPin(LED_STD));
        ret |= ldsLedOff(ldsLedGetConfigPin(LED_GIRL));
    }
    return ret;
}
static int ldsThirdPartyVersionQuery(lds_third_party_frame_t *frame)
{
    int ret = 0;
    size_t str_len = 0;
    const char* version = RT_NULL;

    if(frame == RT_NULL){
        LOG_E("version frame null");
        return -1;
    }

    if(frame->data[0] == 0x00){
        version = ldsMicGetVersion();
    } else {
        version = ldsSmartBaseGetVersion();
    }
    str_len = strlen(version);
    if(str_len == 0) {
        LOG_E("version data len 0");
        return -1;
    }
    frame->dataLen = str_len + 1;
    if(frame->dataLen >= THIRD_PARTY_MAX_DATA_LEN){
        LOG_E("version data len %d error", str_len);
        frame->dataLen = 1;
        ret = -1;
    } else {
        rt_memcpy(&frame->data[1], version, str_len);
    }
    return ret;
}
static int ldsThirdPartyStatusQuery(lds_third_party_frame_t *frame)
{
    int ret = 0;
    if(frame == RT_NULL){
        LOG_E("status frame null");
        return -1;
    }
    frame->data[0] = ldsMicGetStatus();
    frame->data[1] = ldsSmartBaseGetStatus();
    ret = ldsMicGetPowerCtrl();
    if(ret < 0){
        LOG_E("read mic power ctrl pin failed");
        return ret;
    }
    frame->data[2] = ret;
    ret = rt_pin_read(g_powerCtrlUsb);
    if(ret < 0){
        LOG_E("read usb power ctrl pin failed");
        return ret;
    }
    frame->data[3] = ret;
    frame->dataLen = 4;
    ret = 0;
    return ret;
}
/**
 * @brief 处理接收到的协议帧
 * @details 处理完整的接收帧并根据命令类型进行分发
 *
 * @param frame 指向接收帧结构的指针
 * @return int 成功时返回0，失败时返回负错误码
 */
static int ldsThirdPartyProcessFrame(lds_third_party_frame_t *frame)
{
    int ret = -1;
    if (frame == RT_NULL) {
        LOG_E("Invalid frame pointer");
        return -RT_EINVAL;
    }

    LOG_D("Processing frame: cmd=0x%02X, seq=%d, len=%d",
          frame->cmd, frame->seq, frame->dataLen);

    g_errorCount = 0;
    // 这是一个ACK或响应帧。尝试将其与待处理命令匹配。
    rt_mutex_take(&g_thirdPartyMutex, RT_WAITING_FOREVER);
    bool ack_matched = false;
    for (int i = 0; i < THIRD_PARTY_CMD_QUEUE_SIZE; i++) {
        if (g_cmdQueue[i].active && g_cmdQueue[i].seq == frame->seq) {
            LOG_D("ACK received for seq=%d. Removing from queue.", frame->seq);
            g_cmdQueue[i].active = false; // 停用命令
            ack_matched = true;

            // 命令已被确认。我们需要检查是否应该为下一个最旧的命令重启定时器。
            ldsThirdPartyStartRetransmissionTimer();
            break;
        }
    }
    if (!ack_matched) {
        LOG_D("Received ACK for unexpected seq=%d or command type 0x%02X", frame->seq, frame->cmd);
    }
    rt_mutex_release(&g_thirdPartyMutex);

    switch (frame->cmd) {
        case LDS_THIRD_PARTY_CMD_MUTE:
            if(frame->dataLen != 2){
                LOG_E("MUTE command data len %d error", frame->dataLen);
            } else {
                LOG_I("MUTE command: device=%d, mute=%d", frame->data[0], frame->data[1]);
                ret = ldsThirdPartySendMuteCmd(frame->data[0] == 0x00, frame->data[1]);
            }
            break; 
        case LDS_THIRD_PARTY_CMD_EFFECT:
            LOG_I("effect: %d", frame->data[0]);
            ret = ldsThirdPartySendEffectCmd(frame->data[0]);
            break;
        case LDS_THIRD_PARTY_CMD_POWER_CTRL:
            if(frame->dataLen != 2){
                LOG_E("power ctrl command data len %d error", frame->dataLen);
            } else {
                LOG_I("power ctrl command: device=%d, ctrl=%d", frame->data[0], frame->data[1]);
                if(frame->data[0] == 0x00){
                    ret = ldsMicSetPowerCtrl(frame->data[1]);
                } else {
                    rt_pin_write(g_powerCtrlUsb, frame->data[1] ? PIN_HIGH : PIN_LOW);
                    ret = 0;
                }
            }
            break;           
        case LDS_THIRD_PARTY_CMD_VERSION:
            if (frame->dataLen != 1) {
                LOG_E("version command data len %d error", frame->dataLen);
            } else {
                LOG_I("version command: device=%d", frame->data[0]);
                ret = ldsThirdPartyVersionQuery(frame);
            }
            break;

        case LDS_THIRD_PARTY_CMD_STATUS:
            LOG_I("status command");
            ret = ldsThirdPartyStatusQuery(frame);
            break;
        default:
            LOG_W("Unknown command received: 0x%04X", frame->cmd);
            return -RT_ERROR;
    }
    ldsThirdPartySendResponse(frame, ret);
    return 0;
}

/**
 * @brief 启动解析超时定时器
 * @details 启动或重启解析超时定时器以防止状态机挂起
 */
static void ldsThirdPartyStartParseTimer(void)
{
    rt_timer_stop(&g_parseTimer);
    rt_timer_start(&g_parseTimer);
}

/**
 * @brief 重置帧解析状态机
 * @details 将状态机重置为空闲状态并清除缓冲区
 */
static void ldsThirdPartyResetStateMachine(void)
{
    /* 停止解析超时定时器 */
    rt_timer_stop(&g_parseTimer);

    g_rxState = THIRD_PARTY_STATE_IDLE;
    g_rxIndex = 0;
    rt_memset(&g_rxFrame, 0, sizeof(g_rxFrame));
    rt_memset(g_rxBuffer, 0, sizeof(g_rxBuffer));
}

/**
 * @brief 协议帧解析状态机
 * @details 根据协议规范解析传入的字节
 *
 * @param data 指向接收数据缓冲区的指针
 * @param size 接收数据的大小
 * @return int 成功时返回0，失败时返回负错误码
 */
static int ldsThirdPartyParseData(const uint8_t *data, rt_size_t size)
{
    if (data == RT_NULL || size == 0) {
        LOG_E("Invalid data or size");
        return -RT_EINVAL;
    }

    for (rt_size_t i = 0; i < size; i++) {
        uint8_t byte = data[i];
        // LOG_D("state %d", g_rxState);
        switch (g_rxState) {
            case THIRD_PARTY_STATE_IDLE:
                if (byte == THIRD_PARTY_FRAME_HEAD1) {
                    ldsThirdPartyResetStateMachine();
                    g_rxBuffer[g_rxIndex++] = byte;
                    g_rxFrame.head1 = byte;
                    g_rxState = THIRD_PARTY_STATE_HEAD_1;
                    /* 进入解析状态时启动解析超时定时器 */
                    ldsThirdPartyStartParseTimer();
                }
                break;

            case THIRD_PARTY_STATE_HEAD_1:
                if (byte == THIRD_PARTY_FRAME_HEAD2) {
                    g_rxBuffer[g_rxIndex++] = byte;
                    g_rxFrame.head2 = byte;
                    g_rxState = THIRD_PARTY_STATE_CMD;
                    /* 进入解析状态时启动解析超时定时器 */
                    ldsThirdPartyStartParseTimer();
                }
                break;

            case THIRD_PARTY_STATE_CMD:
                g_rxBuffer[g_rxIndex++] = byte;
                g_rxFrame.cmd = byte;
                g_rxState = THIRD_PARTY_STATE_SEQ;
                ldsThirdPartyStartParseTimer();
                break;

            case THIRD_PARTY_STATE_SEQ:
                g_rxBuffer[g_rxIndex++] = byte;
                g_rxFrame.seq = byte;
                g_rxState = THIRD_PARTY_STATE_LEN_H;
                ldsThirdPartyStartParseTimer();
                break;

            case THIRD_PARTY_STATE_LEN_H:
                g_rxBuffer[g_rxIndex++] = byte;
                g_rxFrame.dataLen = (uint16_t)(byte << 8);
                g_rxState = THIRD_PARTY_STATE_LEN_L;
                ldsThirdPartyStartParseTimer();
                break;

            case THIRD_PARTY_STATE_LEN_L:
                g_rxBuffer[g_rxIndex++] = byte;
                g_rxFrame.dataLen |= byte;

                /* 验证数据长度 */
                if (g_rxFrame.dataLen > THIRD_PARTY_MAX_DATA_LEN) {
                    LOG_E("Invalid data length: %d", g_rxFrame.dataLen);
                    ldsThirdPartyResetStateMachine();
                    break;
                }

                if (g_rxFrame.dataLen == 0) {
                    g_rxState = THIRD_PARTY_STATE_CRC16_H;
                } else {
                    g_rxState = THIRD_PARTY_STATE_DATA;
                }
                rt_memset(g_rxFrame.data, 0, sizeof(g_rxFrame.data));
                ldsThirdPartyStartParseTimer();
                break;

            case THIRD_PARTY_STATE_DATA:
                g_rxBuffer[g_rxIndex++] = byte;
                g_rxFrame.data[g_rxIndex - 7] = byte;  /* 数据从索引6开始 */

                if (g_rxIndex >= (6 + g_rxFrame.dataLen)) {
                    g_rxState = THIRD_PARTY_STATE_CRC16_H;
                }
                ldsThirdPartyStartParseTimer();
                break;

            case THIRD_PARTY_STATE_CRC16_H:
                g_rxBuffer[g_rxIndex++] = byte;
                g_rxFrame.crc16 = (uint16_t)(byte << 8);
                g_rxState = THIRD_PARTY_STATE_CRC16_L;
                ldsThirdPartyStartParseTimer();
                break;

            case THIRD_PARTY_STATE_CRC16_L:
                g_rxBuffer[g_rxIndex++] = byte;
                g_rxFrame.crc16 |= byte;

                /* 验证CRC16 */
                uint16_t calculatedCrc16 = ldsUtilCheckCrc16(g_rxBuffer, g_rxIndex - 2);
                if (calculatedCrc16 != g_rxFrame.crc16) {
                    LOG_E("CRC16 mismatch: calculated=0x%04X, received=0x%04X",
                          calculatedCrc16, g_rxFrame.crc16);
                    LOG_HEX("third_party-rx-err", 16, g_rxBuffer, g_rxIndex);
                    ldsThirdPartyResetStateMachine();
                    break;
                }

                // LOG_HEX("third_party-rx", 16, g_rxBuffer, g_rxIndex);
                ldsThirdPartyProcessFrame(&g_rxFrame);
                ldsThirdPartyResetStateMachine();
                break;

            default:
                LOG_E("Invalid state: %d", g_rxState);
                ldsThirdPartyResetStateMachine();
                break;
        }

        /* 防止缓冲区溢出 */
        if (g_rxIndex >= THIRD_PARTY_MAX_FRAME_LEN) {
            LOG_E("Frame buffer overflow");
            ldsThirdPartyResetStateMachine();
            break;
        }
    }

    return 0;
}

/**
 * @brief UART数据处理回调函数
 * @details 向UART驱动程序注册的用于数据处理的回调函数
 *
 * @param dev RT-Thread设备句柄
 * @param data 指向接收数据缓冲区的指针
 * @param size 接收数据的字节大小
 * @return int 成功时返回0，失败时返回负错误码
 */
int ldsThirdPartyProcess(rt_device_t dev, const uint8_t *data, rt_size_t size)
{
    if (data == RT_NULL || size == 0) {
        LOG_E("Invalid data or size");
        return -RT_EINVAL;
    }

    if (dev == RT_NULL) {
        LOG_E("Invalid device handle");
        return -RT_EINVAL;
    }

    LOG_D("Received %d bytes from %s", size, dev->parent.name);
    
    if(ldsAtGetFactoryTestMode()){
        rt_device_write(g_thirdPartyDev, 0, data, size);
        return 0;
    }
    // 这里没有互斥锁，解析在UART上下文中进行。
    // 互斥锁在处理/确认逻辑内部使用。
    int ret = ldsThirdPartyParseData(data, size);

    return ret;
}

/* ================================ 公共API函数 =================== */

/**
 * @brief 初始化第三方通信系统
 * @details 初始化硬件、UART通信、定时器和状态机
 *
 * @return int 成功时返回0，失败时返回负错误码
 *
 * @note 此函数执行完整的第三方系统初始化，包括：
 *       - 电源控制引脚设置
 *       - 带回调的UART接口初始化
 *       - 心跳和重传的定时器配置
 *       - 线程安全的互斥锁初始化
 *       - 状态机和命令队列初始化
 */
int ldsThirdPartyInit(void)
{
    rt_err_t result;

    /* 为线程安全初始化互斥锁 */
    result = rt_mutex_init(&g_thirdPartyMutex, "third_party_mutex", RT_IPC_FLAG_PRIO);
    if (result != RT_EOK) {
        LOG_E("Failed to initialize mutex: %d", result);
        return -RT_ERROR;
    }
    
    /* 初始化电源控制引脚 */
    g_powerCtrlUsb = power_ctrl_pin_init(THIRD_PARTY_POWER_CTRL_USB, PIN_HIGH);
    if (g_powerCtrlUsb < 0) {
        LOG_E("Failed to initialize USB power control pin %s", THIRD_PARTY_POWER_CTRL_USB);
        rt_mutex_detach(&g_thirdPartyMutex);
        return -RT_ERROR;
    }

    /* 使用回调初始化UART */
    g_thirdPartyDev = ldsUartInit(THIRD_PARTY_SERIAL_NAME, LDS_UART_INDEX_7, ldsThirdPartyProcess);
    if (g_thirdPartyDev == RT_NULL) {
        LOG_E("Failed to initialize THIRD_PARTY UART %s", THIRD_PARTY_SERIAL_NAME);
        rt_mutex_detach(&g_thirdPartyMutex);
        return -RT_ERROR;
    }

    /* 初始化重传定时器 */
    rt_timer_init(&g_retransmissionTimer, "thrd_retry",
                  ldsThirdPartyRetransmissionTimeout,
                  RT_NULL,
                  THIRD_PARTY_RESPONSE_TIMEOUT,
                  RT_TIMER_FLAG_SOFT_TIMER | RT_TIMER_FLAG_ONE_SHOT);

    /* 初始化解析超时定时器 */
    rt_timer_init(&g_parseTimer, "thrd_parse",
                  ldsThirdPartyParseTimeout,
                  RT_NULL,
                  THIRD_PARTY_PARSE_TIMEOUT,
                  RT_TIMER_FLAG_SOFT_TIMER | RT_TIMER_FLAG_ONE_SHOT);

    /* 初始化状态机 */
    ldsThirdPartyResetStateMachine();
    g_currentSeq = 0;

    /* 初始化命令队列 */
    ldsThirdPartyInitCmdQueue();


    LOG_I("Third party communication system initialized successfully");
    return 0;
}

/**
 * @brief 反初始化第三方通信系统
 * @details 清理所有资源并停止通信
 *
 * @return int 成功时返回0，失败时返回负错误码
 */
int ldsThirdPartyDeinit(void)
{
    /* 停止定时器 */
    rt_timer_stop(&g_retransmissionTimer);
    rt_timer_stop(&g_parseTimer);
    rt_timer_detach(&g_retransmissionTimer);
    rt_timer_detach(&g_parseTimer);

    /* 重置状态机 */
    ldsThirdPartyResetStateMachine();

    /* 清除命令队列 */
    rt_mutex_take(&g_thirdPartyMutex, RT_WAITING_FOREVER);
    ldsThirdPartyInitCmdQueue();
    rt_mutex_release(&g_thirdPartyMutex);

    /* 关闭UART设备 */
    if (g_thirdPartyDev != RT_NULL) {
        rt_device_close(g_thirdPartyDev);
        g_thirdPartyDev = RT_NULL;
    }

    /* 清理互斥锁 */
    rt_mutex_detach(&g_thirdPartyMutex);

    LOG_I("Third party communication system deinitialized");
    return 0;
}

/* ================================ MSH调试命令 ===================== */
#ifdef RT_USING_FINSH

static int ldsThirdPartyQueueStatus(void)
{
    rt_kprintf("Command Queue Status (Size: %d):\n", THIRD_PARTY_CMD_QUEUE_SIZE);
    bool empty = true;
    for (int i = 0; i < THIRD_PARTY_CMD_QUEUE_SIZE; i++) {
        if (g_cmdQueue[i].active) {
            empty = false;
            rt_kprintf("  Slot %d: [ACTIVE]\n", i);
            rt_kprintf("    seq: %d, cmd: 0x%04X,\n",
                       g_cmdQueue[i].seq, g_cmdQueue[i].cmd);
            rt_kprintf("    retries: %d, sent_at: %u\n",
                       g_cmdQueue[i].retryCount, g_cmdQueue[i].sent_timestamp);
        }
    }
    if (empty) {
        rt_kprintf("  Queue is empty.\n");
    }
    return 0;
}


/**
 * @brief 第三方操作的MSH命令
 * @details 为测试第三方通信提供命令行接口
 *
 * @param argc 参数计数
 * @param argv 参数向量
 * @return int 成功时返回0，失败时返回负错误码
 */
static int ldsThirdPartyCmd(int argc, char **argv)
{
    if (argc < 2) {
        rt_kprintf("Usage: third_party <command> [args...]\n");
        rt_kprintf("Commands:\n");
        rt_kprintf("  init                    - Initialize third party system\n");
        rt_kprintf("  deinit                  - Deinitialize third party system\n");
        rt_kprintf("  reset                   - Reset third party device\n");
        rt_kprintf("  version                 - Query version information\n");
        rt_kprintf("  status                  - Show system status and queue\n");
        rt_kprintf("  test_crc16              - Test CRC16 implementation\n");
        return 0;
    }

    if (rt_strcmp(argv[1], "init") == 0) {
        int ret = ldsThirdPartyInit();
        rt_kprintf("Third party init %s\n", ret == 0 ? "success" : "failed");
        return ret;
    }

    if (rt_strcmp(argv[1], "deinit") == 0) {
        int ret = ldsThirdPartyDeinit();
        rt_kprintf("Third party deinit %s\n", ret == 0 ? "success" : "failed");
        return ret;
    }

    if (rt_strcmp(argv[1], "status") == 0) {
        rt_mutex_take(&g_thirdPartyMutex, RT_WAITING_FOREVER);
        rt_kprintf("Third party System Status:\n");
        rt_kprintf("  Device: %s\n", g_thirdPartyDev ? "initialized" : "not initialized");
        rt_kprintf("  Current Sequence: %d\n", g_currentSeq);
        rt_kprintf("  RX State: %d\n", g_rxState);
        rt_kprintf("  RX Index: %d\n", g_rxIndex);

        /* 检查解析定时器状态 */
        rt_uint8_t parse_timer_state = 0;
        rt_timer_control(&g_parseTimer, RT_TIMER_CTRL_GET_STATE, &parse_timer_state);
        rt_kprintf("  Parse Timer: %s\n", parse_timer_state == RT_TIMER_FLAG_ACTIVATED ? "active" : "inactive");

        ldsThirdPartyQueueStatus();
        rt_mutex_release(&g_thirdPartyMutex);
        return 0;
    }

    rt_kprintf("Unknown command: %s\n", argv[1]);
    return -RT_EINVAL;
}

MSH_CMD_EXPORT_ALIAS(ldsThirdPartyCmd, third_party, Third party communication protocol commands);
#endif