/**
 * @file lds_mic.h
 * @brief LDS麦克风通信协议栈头文件
 * @details 此头文件包含麦克风通信协议函数的声明
 *          和遵循指定大端字节序协议格式的数据结构
 * <AUTHOR> Team
 * @version 1.0
 * @date 2025-07-21
 *
 * @copyright Copyright (c) 2025 LDS
 *
 * 协议规范:
 * - 帧格式: Head(1) + modelId(2) + CMD(2) + SEQ(1) + Addr(1) + LEN(2) + DATA(可变) + SUM(1)
 * - 大端字节序（MSB在前）
 * - Head: 所有帧固定为0x5A
 * - modelId: 设备唯一标识符（0x0001-0xFFFFF）
 * - SEQ: 用于请求/响应匹配的序列号（0x00-0xFF）
 * - Addr: 地址字段（主机=0x00, 从机=0x01/0x02/0x03等）
 * - CMD: 定义操作的功能码
 * - LEN: DATA字段的字节长度
 * - DATA: 可变长度载荷数据
 * - SUM: 校验和（从Head到SUM-1所有字节的和，取低8位）
 */

#ifndef __APPLICATIONS_LDS_MIC_H__
#define __APPLICATIONS_LDS_MIC_H__

/* ================================ Includes ================================ */
#include <stdint.h>
#include <stdbool.h>
#include <rtthread.h>

#ifdef __cplusplus
extern "C" {
#endif

/* ================================ Macros ================================== */

/* ================================ Protocol Constants ===================== */
#define MIC_FRAME_HEAD          0xA5
#define MIC_MIN_FRAME_LEN       10      /**< 不含DATA的最小帧长度 */
#define MIC_MAX_FRAME_LEN       128     /**< 最大帧长度 */
#define MIC_MAX_DATA_LEN        (MIC_MAX_FRAME_LEN - MIC_MIN_FRAME_LEN)

/* ================================ 设备型号ID ======================= */
/**
 * @brief 设备型号ID定义
 * @details 不同设备类型的预定义型号ID
 */
#define LDS_MIC_MODEL_AMPLIFIER_HOST    0x0101  /**< 功放主机设备 */
#define LDS_MIC_MODEL_ARRAY_MIC         0x0102  /**< 阵列麦克风设备 */

/* ================================ 地址定义 ==================== */
/**
 * @brief 协议地址定义
 * @details 通信协议中使用的标准地址
 */
#define LDS_MIC_ADDR_HOST               0x00    /**< 主机设备地址 */
#define LDS_MIC_ADDR_SLAVE_1            0x01    /**< 从机设备1地址 */
#define LDS_MIC_ADDR_SLAVE_2            0x02    /**< 从机设备2地址 */
#define LDS_MIC_ADDR_SLAVE_3            0x03    /**< 从机设备3地址 */
#define LDS_MIC_ADDR_SLAVE_BROADCAST    0xF1    /**< 从机广播地址 */
#define LDS_MIC_ADDR_UNUSED             0xEE    /**< 未使用地址 */
#define LDS_MIC_ADDR_GLOBAL_BROADCAST   0xFF    /**< 全局广播地址 */

/* ================================ 类型定义 ======================== */

/**
 * @brief 麦克风命令枚举
 * @details 定义麦克风通信支持的命令类型
 */
typedef enum {
    LDS_MIC_CMD_FACTORY_RESET = 0x0201,     /**< 恢复出厂设置命令 */
    LDS_MIC_CMD_HEARTBEAT = 0x0202,         /**< 心跳命令 */
    LDS_MIC_CMD_REBOOT = 0x0203,            /**< 重启设备命令 */
    LDS_MIC_CMD_VERSION_QUERY = 0x0204,     /**< 查询版本信息命令 */
    LDS_MIC_CMD_SET_ARRAY_PARAMS = 0x0301,  /**< 设置阵列麦克风参数命令 */
} LDS_MIC_CMD_E;

/**
 * @brief 阵列麦克风参数类型
 * @details SET_ARRAY_PARAMS命令的参数类型
 */
typedef enum {
    LDS_MIC_PARAM_MUTE_CONTROL = 0x01,      /**< 静音控制参数 */
    LDS_MIC_PARAM_SOUND_MODE = 0x02,        /**< 声音模式参数 */
} LDS_MIC_PARAM_TYPE_E;

/**
 * @brief 静音控制状态
 * @details 静音控制参数的值
 */
typedef enum {
    LDS_MIC_MUTE_OFF = 0x00,                /**< 取消静音状态 */
    LDS_MIC_MUTE_ON = 0x01,                 /**< 静音状态 */
    LDS_MIC_MUTE_QUERY = 0xFF,              /**< 查询当前静音状态 */
    LDS_MIC_MUTE_MAX,                       /**< 用于验证的最大值 */
} LDS_MIC_MUTE_E;

/**
 * @brief 声音模式类型
 * @details 声音模式参数的值
 */
typedef enum {
    LDS_MIC_SOUND_MODE_STANDARD = 0x00,     /**< 标准声音模式 */
    LDS_MIC_SOUND_MODE_FEMALE = 0x01,       /**< 女声优化模式 */
    LDS_MIC_SOUND_MODE_MALE = 0x02,         /**< 男声优化模式 */
    LDS_MIC_SOUND_MODE_QUERY = 0xFF,        /**< 查询当前声音模式 */
    LDS_MIC_SOUND_MODE_MAX,                 /**< 用于验证的最大值 */
} LDS_MIC_SOUND_MODE_E;

/**
 * @brief 协议帧结构体
 * @details 表示完整协议帧的结构体
 */
typedef struct {
    uint8_t head;                           /**< 帧头（0x5A） */
    uint16_t modelId;                       /**< 设备型号ID（大端序） */
    uint16_t cmd;                           /**< 命令码（大端序） */
    uint8_t seq;                            /**< 序列号 */
    uint8_t addr;                           /**< 地址字段 */
    uint16_t dataLen;                       /**< 数据长度（大端序） */
    uint8_t data[MIC_MAX_DATA_LEN];         /**< 数据载荷 */
    uint8_t checksum;                       /**< 帧校验和 */
} lds_mic_frame_t;

/* ================================ 函数声明 =================== */

/**
 * @brief 获取麦克风状态
 * @details 检索麦克风的当前状态
 *
 * @return uint8_t 状态字节
 */
uint8_t ldsMicGetStatus(void);
/**
 * @brief 获取麦克风电源状态
 * @details 检索麦克风的当前电源状态
 *
 * @return int 成功时返回0或1，失败时返回负错误码
 */
int ldsMicGetPowerCtrl(void);
/**
 * @brief 控制麦克风电源
 * @details 打开或关闭麦克风电源
 *
 * @param on true表示打开，false表示关闭
 * @return int 成功返回0，失败返回负错误码
 */
int ldsMicSetPowerCtrl(int on);
/**
 * @brief 获取麦克风版本信息
 * @details 检索麦克风设备的版本信息
 *
 * @return const char* 指向版本字符串的指针
 */
const char *ldsMicGetVersion(void);
/**
 * @brief 初始化麦克风通信系统
 * @details 初始化硬件、UART通信、定时器和状态机
 *
 * @return int 成功返回0，失败返回负错误码
 *
 * @note 此函数执行完整的麦克风系统初始化，包括：
 *       - 电源控制引脚设置
 *       - 带回调的UART接口初始化
 *       - 心跳和命令重传的定时器配置
 *       - 线程安全的互斥锁初始化
 *       - 状态机和命令队列初始化
 *
 * @example
 * @code
 * int result = ldsMicInit();
 * if (result != 0) {
 *     rt_kprintf("Microphone initialization failed: %d\n", result);
 * }
 * @endcode
 */
int ldsMicInit(void);

/**
 * @brief 反初始化麦克风通信系统
 * @details 清理所有资源并停止通信
 *
 * @return int 成功返回0，失败返回负错误码
 *
 * @note 此函数应在系统关闭前或不再需要麦克风通信时调用
 */
int ldsMicDeinit(void);

/**
 * @brief 查询设备版本信息
 * @details 从指定设备请求版本信息
 *
 * @return int 成功返回0，失败返回负错误码
 *
 * @note 响应将在数据字段中包含版本信息
 */
int ldsMicQueryVersion(void);

/**
 * @brief 查询设备状态信息
 * @details 从指定设备请求状态信息
 *
 * @return int 成功返回0，失败返回负错误码
 *
 * @note 响应将在数据字段中包含状态信息
 */
int ldsMicQueryStatus(void);

/**
 * @brief 设置麦克风静音控制
 * @details 控制麦克风阵列的静音状态
 *
 * @param addr 目标设备地址
 * @return int 成功返回0，失败返回负错误码
 */
int ldsMicSetMuteByAddr(uint8_t addr);
/**
 * @brief 设置麦克风静音控制
 * @details 控制麦克风阵列的静音状态
 *
 * @param addr 目标设备地址
 * @param muteState 来自LDS_MIC_MUTE_E的静音状态
 * @return int 成功返回0，失败返回负错误码
 *
 * @example
 * @code
 * // 静音麦克风
 * int result = ldsMicSetMuteControl(LDS_MIC_ADDR_SLAVE_1,
 *                                   LDS_MIC_MUTE_ON);
 * @endcode
 */
int ldsMicSetMuteControl(uint8_t addr, LDS_MIC_MUTE_E muteState);

/**
 * @brief 设置麦克风声音模式
 * @details 设置麦克风阵列的声音处理模式
 *
 * @param soundMode 来自LDS_MIC_SOUND_MODE_E的声音模式
 * @return int 成功返回0，失败返回负错误码
 *
 * @example
 * @code
 * // 设置女声模式
 * int result = ldsMicSetSoundMode(LDS_MIC_SOUND_MODE_FEMALE);
 * @endcode
 */
int ldsMicSetSoundMode(LDS_MIC_SOUND_MODE_E soundMode);

#ifdef __cplusplus
}
#endif

#endif /* __APPLICATIONS_LDS_MIC_H__ */
