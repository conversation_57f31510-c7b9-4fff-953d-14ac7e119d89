/**
 * @file lds_uart.h
 * @brief RT-Thread应用层LDS UART驱动头文件
 * @details 此头文件提供支持DMA的UART通信功能，
 *          包括回调注册和基于消息队列的数据处理功能。
 * <AUTHOR> Team
 * @date 2025-07-09
 * @version 1.0
 *
 * @copyright Copyright (c) 2025 LDS
 *
 * 功能特性:
 * - 多UART支持（最多7个UART接口）
 * - 基于DMA的接收和轮询发送
 * - 基于回调的数据处理
 * - 高效的消息队列数据处理
 * - 线程安全操作
 * - RT-Thread集成
 */

#ifndef __APPLICATIONS_LDS_UART_H__
#define __APPLICATIONS_LDS_UART_H__

/* ================================ Includes ================================ */
#include <rtthread.h>
#include <stdint.h>
#include <stdbool.h>

#ifdef __cplusplus
extern "C" {
#endif

/* ================================ Macros ================================== */

/* ================================ Type Definitions ======================== */

/**
 * @brief UART接口索引枚举
 * @details 定义LDS UART驱动可用的UART接口索引
 */
typedef enum {
    LDS_UART_INDEX_1 = 0,    /**< UART接口1 */
    LDS_UART_INDEX_2,        /**< UART接口2 */
    LDS_UART_INDEX_3,        /**< UART接口3 */
    LDS_UART_INDEX_4,        /**< UART接口4 */
    LDS_UART_INDEX_5,        /**< UART接口5 */
    LDS_UART_INDEX_6,        /**< UART接口6 */
    LDS_UART_INDEX_7,        /**< UART接口7 */
    LDS_UART_INDEX_MAX,      /**< 最大UART索引（边界检查） */
} LDS_UART_INDEX_E;

/**
 * @brief UART回调函数类型
 * @details 处理接收到的UART数据的回调函数原型
 *
 * @param dev RT-Thread设备句柄
 * @param data 指向接收数据缓冲区的指针
 * @param size 接收数据的字节大小
 * @return int 返回码（0表示成功，负值表示错误）
 */
typedef int (*ldsUartCb_t)(rt_device_t dev, const uint8_t* data, rt_size_t size);

/* ================================ Function Declarations =================== */

/**
 * @brief 使用回调函数初始化UART接口
 * @details 初始化指定的UART接口，启用DMA接收并
 *          注册用于数据处理的回调函数
 *
 * @param uart_name UART设备名称（例如："uart1", "uart2"）
 * @param index 来自LDS_UART_INDEX_E的UART接口索引
 * @param cb 处理接收数据的回调函数
 * @return rt_device_t 成功时返回RT-Thread设备句柄，失败时返回RT_NULL
 *
 * @note UART将使用RT_DEVICE_FLAG_RDWR | RT_DEVICE_FLAG_DMA_RX标志打开
 * @note 如果尚未完成内部初始化，此函数会自动调用内部初始化
 *
 * @example
 * @code
 * static int myUartCallback(rt_device_t dev, const char* data, rt_size_t size) {
 *     // 处理接收到的数据
 *     rt_kprintf("接收到 %d 字节: %.*s\n", size, size, data);
 *     return 0;
 * }
 *
 * rt_device_t result = ldsUartInit("uart1", LDS_UART_INDEX_1, myUartCallback);
 * if (result == RT_NULL) {
 *     rt_kprintf("UART初始化失败\n");
 * }
 * @endcode
 */
rt_device_t ldsUartInit(const char* uart_name, LDS_UART_INDEX_E index, ldsUartCb_t cb);

#ifdef __cplusplus
}
#endif

#endif /* __APPLICATIONS_LDS_UART_H__ */