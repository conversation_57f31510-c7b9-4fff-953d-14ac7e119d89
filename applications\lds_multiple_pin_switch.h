#ifndef __LDS_MULTIPLE_PIN_SWITCH_H__
#define __LDS_MULTIPLE_PIN_SWITCH_H__

#ifdef __cplusplus
extern "C" {
#endif

typedef struct
{
    bool active_high;        // 开关的有效电平
    uint8_t pin_count;       // 开关使用的引脚数量
    rt_base_t pins[7];       // 引脚编号数组，最多7个引脚
} lds_multiple_pin_switch_t;

typedef enum
{
    LDS_MULTIPLE_PIN_INPUT_NORMAL = 0, // 正常模式，引脚为高电平时开关有效
    LDS_MULTIPLE_PIN_INPUT_PULL_UP, // 上拉模式，引脚为低电平时开关有效
    LDS_MULTIPLE_PIN_INPUT_PULL_DOWN, // 下拉模式，引脚为高电平时开关有效

} LDS_MULTIPLE_PIN_INPUT_MODE_E;

/**
 * @brief 获取多引脚开关的当前状态/位置
 *
 * @param hd 指向多引脚开关处理器结构体的指针
 * @return int8_t 多引脚开关的当前位置，负值表示错误
 */
int8_t ldsMultiplePinSwitchGet(lds_multiple_pin_switch_t * hd);
/**
 * @brief 初始化多引脚开关接口
 *
 * @param hd 指向多引脚开关句柄结构体的指针
 * @param pin_names 包含要初始化的逗号分隔引脚名称的字符串
 * @param pin_count 要初始化的引脚数量
 * @param active_high 如果引脚为高电平有效则为true，低电平有效则为false
 * @param mode 引脚的输入模式（正常、上拉、下拉）
 * @return int 成功返回0，失败返回负错误码
 */
int ldsMultiplePinSwitchInit(lds_multiple_pin_switch_t *hd, const char *pin_names, uint8_t pin_count, bool active_high, LDS_MULTIPLE_PIN_INPUT_MODE_E mode);
int lds_multiple_pin_test(void);
#ifdef __cplusplus
}
#endif
#endif // !__LDS_MULTIPLE_PIN_SWITCH_H__