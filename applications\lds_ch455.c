/**
 * @file lds_ch455.c
 * @brief CH455芯片驱动实现
 * @details 全面的CH455基于I2C的按键扫描器和LED控制器驱动
 * <AUTHOR> Team
 * @version 1.0
 * @date 2025-07-10
 *
 * @copyright Copyright (c) 2025 LDS
 */

#include <string.h>
#include <stdlib.h>
#include <rtthread.h>
#include <rtdevice.h>
#include "lds_ch455.h"

#define DBG_TAG "CH455"
#define DBG_LVL DBG_INFO
#include <rtdbg.h>

// 待办：测试按键扫描
// #define LDS_CH455_ADD_TEST_CMD
/* CH455内部常量 */
#define LDS_CH455_THREAD_STACK_SIZE     (1024)      /**< 按键扫描线程栈大小 */
#define LDS_CH455_THREAD_PRIORITY       (10)        /**< 按键扫描线程优先级 */
#define LDS_CH455_THREAD_TIMESLICE      (5)         /**< 按键扫描线程时间片 */
#define LDS_CH455_MAX_INSTANCES         (4)         /**< 最大设备实例数 */

/* 7段数码管显示模式 */
static const uint8_t kDigitPatterns[16] = {
    0x3F,  /* 0: 0011 1111 */
    0x06,  /* 1: 0000 0110 */
    0x5B,  /* 2: 0101 1011 */
    0x4F,  /* 3: 0100 1111 */
    0x66,  /* 4: 0110 0110 */
    0x6D,  /* 5: 0110 1101 */
    0x7D,  /* 6: 0111 1101 */
    0x07,  /* 7: 0000 0111 */
    0x7F,  /* 8: 0111 1111 */
    0x6F,  /* 9: 0110 1111 */
    0x77,  /* A: 0111 0111 */
    0x7C,  /* b: 0111 1100 */
    0x58,  /* C: 0101 1000 */
    0x5E,  /* d: 0101 1110 */
    0x79,  /* E: 0111 1001 */
    0x71   /* F: 0111 0001 */
};

/**
 * @brief CH455设备结构体
 * @details 包含所有设备状态和配置的内部设备结构体
 */
typedef struct lds_ch455_device
{
    /* 配置 */
    lds_ch455_config_t config;             /**< 设备配置 */

    /* I2C通信 */
    struct rt_i2c_bus_device *i2c_bus;     /**< I2C总线设备句柄 */
    rt_mutex_t i2c_mutex;                  /**< I2C访问互斥锁 */

    /* 按键扫描 */
    rt_thread_t scan_thread;               /**< 按键扫描线程 */
    volatile bool scan_active;             /**< 扫描活动标志 */
    uint32_t last_key_state;               /**< 上次按键状态 */
    rt_tick_t key_press_time[LDS_CH455_MAX_KEYS]; /**< 按键按下时间戳 */

    /* 设备状态 */
    volatile bool initialized;             /**< 初始化标志 */
    uint8_t current_brightness;            /**< 当前亮度设置 */
    uint8_t display_buffer[LDS_CH455_MAX_DIGITS]; /**< 显示缓冲区 */

    /* 统计信息 */
    lds_ch455_stats_t stats;               /**< 性能统计 */

    /* 同步 */
    rt_mutex_t device_mutex;               /**< 设备访问互斥锁 */

} lds_ch455_device_t;

/* 全局设备实例 */
static lds_ch455_device_t *g_ch455_devices[LDS_CH455_MAX_INSTANCES] = {RT_NULL};
static rt_mutex_t g_instances_mutex = RT_NULL;

/**
 * @brief 初始化全局资源
 * @details 初始化实例管理的全局互斥锁
 *
 * @return int 成功返回0，错误返回负值
 */
static int ldsCh455GlobalInit(void)
{
    static bool global_initialized = false;
    
    if (global_initialized) {
        return 0;
    }
    
    g_instances_mutex = rt_mutex_create("ch455_inst", RT_IPC_FLAG_PRIO);
    if (g_instances_mutex == RT_NULL) {
        LOG_E("Failed to create instances mutex");
        return -RT_ERROR;
    }
    
    global_initialized = true;
    LOG_D("CH455 global resources initialized");
    return 0;
}

/**
 * @brief 查找空闲设备槽
 * @details 为新设备实例查找可用槽位
 *
 * @return int 成功返回槽位索引，错误返回负值
 */
static int ldsCh455FindFreeSlot(void)
{
    rt_err_t result = rt_mutex_take(g_instances_mutex, RT_WAITING_FOREVER);
    if (result != RT_EOK) {
        return -RT_ERROR;
    }
    
    int slot = -1;
    for (int i = 0; i < LDS_CH455_MAX_INSTANCES; i++) {
        if (g_ch455_devices[i] == RT_NULL) {
            slot = i;
            break;
        }
    }
    
    rt_mutex_release(g_instances_mutex);
    return slot;
}

/**
 * @brief 注册设备实例
 * @details 在全局实例数组中注册设备
 *
 * @param device 设备指针
 * @return int 成功返回槽位索引，错误返回负值
 */
static int ldsCh455RegisterDevice(lds_ch455_device_t *device)
{
    int slot = ldsCh455FindFreeSlot();
    if (slot < 0) {
        LOG_E("No free device slots available");
        return -RT_ERROR;
    }
    
    rt_err_t result = rt_mutex_take(g_instances_mutex, RT_WAITING_FOREVER);
    if (result != RT_EOK) {
        return -RT_ERROR;
    }
    
    g_ch455_devices[slot] = device;
    rt_mutex_release(g_instances_mutex);
    
    LOG_I("Device registered in slot %d", slot);
    return slot;
}

/**
 * @brief 注销设备实例
 * @details 从全局实例数组中移除设备
 *
 * @param device 设备指针
 */
static void ldsCh455UnregisterDevice(lds_ch455_device_t *device)
{
    rt_err_t result = rt_mutex_take(g_instances_mutex, RT_WAITING_FOREVER);
    if (result != RT_EOK) {
        return;
    }
    
    for (int i = 0; i < LDS_CH455_MAX_INSTANCES; i++) {
        if (g_ch455_devices[i] == device) {
            g_ch455_devices[i] = RT_NULL;
            LOG_I("Device unregistered from slot %d", i);
            break;
        }
    }
    
    rt_mutex_release(g_instances_mutex);
}

/**
 * @brief 向CH455写入16位命令
 * @details 使用官方协议向CH455发送16位命令
 *
 * @param device 设备句柄
 * @param cmd 16位命令
 * @return int 成功返回0，错误返回负值
 */
static int ldsCh455WriteCommand(lds_ch455_device_t *device, uint16_t cmd)
{
    if (device == RT_NULL || !device->initialized) {
        return LDS_CH455_ERR_NOT_INIT;
    }

    rt_err_t result = rt_mutex_take(device->i2c_mutex,
                                   rt_tick_from_millisecond(device->config.i2c_timeout_ms));
    if (result != RT_EOK) {
        LOG_W("I2C mutex timeout");
        return LDS_CH455_ERR_TIMEOUT;
    }

    int ret = LDS_CH455_ERR_NONE;

    /* 根据官方CH455协议准备命令字节 */
    uint8_t addr_byte = ((uint8_t)(cmd >> 7) & LDS_CH455_I2C_MASK) | device->config.i2c_addr;
    uint8_t data_byte = (uint8_t)cmd;

    for (int retry = 0; retry < device->config.i2c_retry_count; retry++) {
        /* 使用RT-Thread I2C传输进行精确控制 */
        struct rt_i2c_msg msgs[1];

        msgs[0].addr = addr_byte >> 1;  /* 提取7位地址 */
        msgs[0].flags = RT_I2C_WR;
        msgs[0].len = 1;
        msgs[0].buf = &data_byte;

        rt_ssize_t result_transfer = rt_i2c_transfer(device->i2c_bus, msgs, 1);

        if (result_transfer == 1) {
            device->stats.i2c_tx_count++;
            ret = LDS_CH455_ERR_NONE;
            break;
        } else {
            device->stats.i2c_error_count++;
            ret = LDS_CH455_ERR_I2C_FAIL;
            LOG_W("I2C command write failed, retry %d/%d", retry + 1, device->config.i2c_retry_count);
            rt_thread_mdelay(2); /* 重试前短暂延迟 */
        }
    }

    rt_mutex_release(device->i2c_mutex);
    return ret;
}

/**
 * @brief 向CH455写入显示数据
 * @details 向特定数字位置写入显示数据
 *
 * @param device 设备句柄
 * @param digit_pos 数字位置（0-3）
 * @param data 显示数据字节
 * @return int 成功返回0，错误返回负值
 */
static int ldsCh455WriteDisplay(lds_ch455_device_t *device, uint8_t digit_pos, uint8_t data)
{
    if (device == RT_NULL || !device->initialized) {
        return LDS_CH455_ERR_NOT_INIT;
    }

    if (digit_pos >= LDS_CH455_MAX_DIGITS) {
        return LDS_CH455_ERR_INVALID_PARAM;
    }

    /* 根据数字位置计算显示命令 */
    uint16_t display_cmd;
    switch (digit_pos) {
        case 0: display_cmd = LDS_CH455_DIG0 | data; break;
        case 1: display_cmd = LDS_CH455_DIG1 | data; break;
        case 2: display_cmd = LDS_CH455_DIG2 | data; break;
        case 3: display_cmd = LDS_CH455_DIG3 | data; break;
        default: return LDS_CH455_ERR_INVALID_PARAM;
    }

    return ldsCh455WriteCommand(device, display_cmd);
}

/**
 * @brief 从CH455读取数据
 * @details 从CH455读取原始数据。这是一个通用读取函数，
 *          使用GET_KEY命令，适用于通信检查。
 *
 * @param device 设备句柄
 * @param data 存储读取数据的指针
 * @param len 要读取的数据长度
 * @return int 成功返回0，错误返回负值
 */
static int ldsCh455ReadData(lds_ch455_device_t *device, uint8_t *data, rt_size_t len)
{
    if (device == RT_NULL || !device->initialized || data == RT_NULL) {
        return LDS_CH455_ERR_INVALID_PARAM;
    }

    rt_err_t result = rt_mutex_take(device->i2c_mutex,
                                   rt_tick_from_millisecond(device->config.i2c_timeout_ms));
    if (result != RT_EOK) {
        LOG_W("I2C mutex timeout");
        return LDS_CH455_ERR_TIMEOUT;
    }

    int ret = LDS_CH455_ERR_NONE;

    uint8_t addr_byte = ((uint8_t)(LDS_CH455_GET_KEY >> 7) & LDS_CH455_I2C_MASK) | 0x01 | device->config.i2c_addr;

    for (int retry = 0; retry < device->config.i2c_retry_count; retry++) {
        /* 使用RT-Thread I2C传输进行数据读取 */
        struct rt_i2c_msg msgs[1];
        msgs[0].addr = addr_byte >> 1;
        msgs[0].flags = RT_I2C_RD;
        msgs[0].len = len;
        msgs[0].buf = data;

        rt_ssize_t result_transfer = rt_i2c_transfer(device->i2c_bus, msgs, 1);

        if (result_transfer == 1) {
            device->stats.i2c_rx_count++;
            ret = LDS_CH455_ERR_NONE;
            break;
        } else {
            device->stats.i2c_error_count++;
            ret = LDS_CH455_ERR_I2C_FAIL;
            LOG_W("I2C data read failed, retry %d/%d", retry + 1, device->config.i2c_retry_count);
            rt_thread_mdelay(1);
        }
    }

    rt_mutex_release(device->i2c_mutex);
    return ret;
}
/**
 * @brief 从CH455读取按键数据
 * @details 使用官方协议从CH455读取按键扫描结果
 *
 * @param device 设备句柄
 * @param key_data 存储按键数据的指针
 * @return int 成功返回0，错误返回负值
 */
static int ldsCh455ReadKey(lds_ch455_device_t *device, uint8_t *key_data)
{
    if (device == RT_NULL || !device->initialized || key_data == RT_NULL) {
        return LDS_CH455_ERR_INVALID_PARAM;
    }

    return ldsCh455ReadData(device, key_data, 1);
}

/**
 * @brief 按键扫描线程函数
 * @details 用于连续按键扫描和事件处理的后台线程
 *
 * @param parameter 作为参数传递的设备句柄
 */
static void ldsCh455ScanThread(void *parameter)
{
    lds_ch455_device_t *device = (lds_ch455_device_t *)parameter;
    uint32_t current_key_state = 0;
    rt_tick_t current_time;

    LOG_I("CH455 key scanning thread started");

    while (device->scan_active) {
        /* 检查当前模式是否启用键盘扫描 */
        if (device->config.mode != LDS_CH455_MODE_KEYBOARD_ONLY &&
            device->config.mode != LDS_CH455_MODE_COMBINED) {
            rt_thread_mdelay(device->config.scan_interval_ms);
            continue;
        }

        current_time = rt_tick_get();
        device->stats.last_scan_time = current_time;
        device->stats.key_scan_count++;

        /* 从CH455读取按键状态 */
        uint8_t key_data = 0;
        int result = ldsCh455ReadKey(device, &key_data);

        if (result == LDS_CH455_ERR_NONE) {
            /* CH455直接返回按键码，转换为位掩码 */
            if (key_data != 0xFF) {  /* 0xFF表示没有按键按下 */
                uint8_t key_code = key_data & 0x3F;  /* 提取按键码（0-63） */
                if (key_code < LDS_CH455_MAX_KEYS) {
                    current_key_state = (1U << key_code);
                } else {
                    current_key_state = 0;
                }
            } else {
                current_key_state = 0;  /* 没有按键按下 */
            }

            /* 处理按键变化 */
            uint32_t key_changes = current_key_state ^ device->last_key_state;

            if (key_changes != 0) {
                for (int key = 0; key < LDS_CH455_MAX_KEYS; key++) {
                    uint32_t key_mask = (1U << key);

                    if (key_changes & key_mask) {
                        bool key_pressed = (current_key_state & key_mask) != 0;

                        if (key_pressed) {
                            /* 按键按下 */
                            device->key_press_time[key] = current_time;
                            device->stats.key_press_count++;
                            device->stats.last_key_time = current_time;

                            if (device->config.key_callback) {
                                device->config.key_callback(key, LDS_CH455_KEY_PRESSED,
                                                           device->config.user_data);
                                device->stats.callback_count++;
                            }

                            LOG_D("Key %d pressed", key);
                        } else {
                            /* 按键释放 */
                            rt_tick_t press_duration = current_time - device->key_press_time[key];
                            lds_ch455_key_event_t event = LDS_CH455_KEY_RELEASED;

                            /* 检查长按（>1秒） */
                            if (LDS_CH455_TICK_TO_MS(press_duration) > 1000) {
                                event = LDS_CH455_KEY_LONG_PRESSED;
                            }

                            if (device->config.key_callback) {
                                device->config.key_callback(key, event, device->config.user_data);
                                device->stats.callback_count++;
                            }

                            LOG_D("Key %d released (duration: %d ms)", key,
                                  LDS_CH455_TICK_TO_MS(press_duration));
                        }
                    }
                }

                device->last_key_state = current_key_state;
            }
        } else {
            LOG_W("Key scan failed: %d", result);
        }

        /* 按扫描间隔休眠 */
        rt_thread_mdelay(device->config.scan_interval_ms);
    }

    LOG_I("CH455 key scanning thread stopped");
}

/**
 * @brief 初始化CH455硬件
 * @details 执行硬件初始化和配置
 *
 * @param device 设备句柄
 * @return int 成功返回0，错误返回负值
 */
static int ldsCh455HardwareInit(lds_ch455_device_t *device)
{
    int result;

    /* 复位CH455 */
    result = ldsCh455WriteCommand(device, LDS_CH455_CMD_RESET);
    if (result != LDS_CH455_ERR_NONE) {
        LOG_E("Failed to reset CH455: %d", result);
        return result;
    }

    rt_thread_mdelay(10); /* 等待复位完成 */

    /* 启用系统 */
    result = ldsCh455WriteCommand(device, LDS_CH455_CMD_SYSTEM_EN);
    if (result != LDS_CH455_ERR_NONE) {
        LOG_E("Failed to enable CH455 system: %d", result);
        return result;
    }

    /* 设置初始亮度 */
    result = ldsCh455SetBrightness((lds_ch455_handle_t)device, device->config.brightness);
    if (result != LDS_CH455_ERR_NONE) {
        LOG_E("Failed to set initial brightness: %d", result);
        return result;
    }

    /* 如果启用显示模式则清除显示 */
    if (device->config.mode == LDS_CH455_MODE_DISPLAY_ONLY ||
        device->config.mode == LDS_CH455_MODE_COMBINED) {
        for (int i = 0; i < LDS_CH455_MAX_DIGITS; i++) {
            result = ldsCh455WriteDisplay(device, i, 0x00);
            if (result != LDS_CH455_ERR_NONE) {
                LOG_E("Failed to clear digit %d: %d", i, result);
                return result;
            }
            device->display_buffer[i] = 0x00;
        }
    }

    LOG_I("CH455 hardware initialized successfully");
    return LDS_CH455_ERR_NONE;
}

/**
 * @brief Validate configuration parameters
 * @details Checks configuration parameters for validity
 *
 * @param config Configuration structure
 * @return int 0 if valid, negative on error
 */
static int ldsCh455ValidateConfig(const lds_ch455_config_t *config)
{
    if (config == RT_NULL) {
        LOG_E("Configuration is RT_NULL");
        return LDS_CH455_ERR_INVALID_PARAM;
    }

    if (config->i2c_bus_name == RT_NULL) {
        LOG_E("I2C bus name is RT_NULL");
        return LDS_CH455_ERR_INVALID_PARAM;
    }

    if (config->brightness > LDS_CH455_MAX_BRIGHTNESS) {
        LOG_E("Invalid brightness: %d (max: %d)", config->brightness, LDS_CH455_MAX_BRIGHTNESS);
        return LDS_CH455_ERR_INVALID_PARAM;
    }

    if (config->i2c_timeout_ms == 0 || config->i2c_timeout_ms > 10000) {
        LOG_E("Invalid I2C timeout: %d ms", config->i2c_timeout_ms);
        return LDS_CH455_ERR_INVALID_PARAM;
    }

    if (config->i2c_retry_count == 0 || config->i2c_retry_count > 10) {
        LOG_E("Invalid I2C retry count: %d", config->i2c_retry_count);
        return LDS_CH455_ERR_INVALID_PARAM;
    }

    if (config->scan_interval_ms == 0 || config->scan_interval_ms > 1000) {
        LOG_E("Invalid scan interval: %d ms", config->scan_interval_ms);
        return LDS_CH455_ERR_INVALID_PARAM;
    }

    return LDS_CH455_ERR_NONE;
}

/* ================================ Public API Functions ================================ */

lds_ch455_handle_t ldsCh455Init(const lds_ch455_config_t *config)
{
    int result;

    /* Initialize global resources */
    result = ldsCh455GlobalInit();
    if (result != 0) {
        LOG_E("Failed to initialize global resources");
        return RT_NULL;
    }

    /* Validate configuration */
    result = ldsCh455ValidateConfig(config);
    if (result != LDS_CH455_ERR_NONE) {
        LOG_E("Invalid configuration");
        return RT_NULL;
    }

    /* Allocate device structure */
    lds_ch455_device_t *device = (lds_ch455_device_t *)rt_malloc(sizeof(lds_ch455_device_t));
    if (device == RT_NULL) {
        LOG_E("Failed to allocate device memory");
        return RT_NULL;
    }

    /* Initialize device structure */
    rt_memset(device, 0, sizeof(lds_ch455_device_t));
    rt_memcpy(&device->config, config, sizeof(lds_ch455_config_t));

    /* Find I2C bus */
    device->i2c_bus = (struct rt_i2c_bus_device *)rt_device_find(config->i2c_bus_name);
    if (device->i2c_bus == RT_NULL) {
        LOG_E("I2C bus '%s' not found", config->i2c_bus_name);
        rt_free(device);
        return RT_NULL;
    }

    /* Create mutexes */
    device->i2c_mutex = rt_mutex_create("ch455_i2c", RT_IPC_FLAG_PRIO);
    if (device->i2c_mutex == RT_NULL) {
        LOG_E("Failed to create I2C mutex");
        rt_free(device);
        return RT_NULL;
    }

    device->device_mutex = rt_mutex_create("ch455_dev", RT_IPC_FLAG_PRIO);
    if (device->device_mutex == RT_NULL) {
        LOG_E("Failed to create device mutex");
        rt_mutex_delete(device->i2c_mutex);
        rt_free(device);
        return RT_NULL;
    }

    /* Initialize device state */
    device->initialized = true;
    device->scan_active = false;
    device->current_brightness = config->brightness;
    device->last_key_state = 0;

    /* Initialize statistics */
    rt_memset((void*)&device->stats, 0, sizeof(device->stats));

    /* Initialize hardware */
    result = ldsCh455HardwareInit(device);
    if (result != LDS_CH455_ERR_NONE) {
        LOG_E("Hardware initialization failed: %d", result);
        rt_mutex_delete(device->device_mutex);
        rt_mutex_delete(device->i2c_mutex);
        rt_free(device);
        return RT_NULL;
    }

    /* Register device instance */
    result = ldsCh455RegisterDevice(device);
    if (result < 0) {
        LOG_E("Failed to register device instance");
        rt_mutex_delete(device->device_mutex);
        rt_mutex_delete(device->i2c_mutex);
        rt_free(device);
        return RT_NULL;
    }

    /* Start key scanning thread if enabled and keyboard mode is active */
    if (config->auto_scan_enable &&
        (config->mode == LDS_CH455_MODE_KEYBOARD_ONLY || config->mode == LDS_CH455_MODE_COMBINED)) {
        device->scan_active = true;

        char thread_name[RT_NAME_MAX];
        rt_snprintf(thread_name, sizeof(thread_name), "ch455_%d", result);

        device->scan_thread = rt_thread_create(thread_name,
                                              ldsCh455ScanThread,
                                              device,
                                              LDS_CH455_THREAD_STACK_SIZE,
                                              LDS_CH455_THREAD_PRIORITY,
                                              LDS_CH455_THREAD_TIMESLICE);

        if (device->scan_thread == RT_NULL) {
            LOG_E("Failed to create scanning thread");
            device->scan_active = false;
            ldsCh455UnregisterDevice(device);
            rt_mutex_delete(device->device_mutex);
            rt_mutex_delete(device->i2c_mutex);
            rt_free(device);
            return RT_NULL;
        }

        result = rt_thread_startup(device->scan_thread);
        if (result != RT_EOK) {
            LOG_E("Failed to start scanning thread");
            rt_thread_delete(device->scan_thread);
            device->scan_active = false;
            ldsCh455UnregisterDevice(device);
            rt_mutex_delete(device->device_mutex);
            rt_mutex_delete(device->i2c_mutex);
            rt_free(device);
            return RT_NULL;
        }

        LOG_I("Key scanning thread started");
    }

    LOG_I("CH455 device initialized successfully (I2C: %s, Addr: 0x%02X)",
          config->i2c_bus_name, config->i2c_addr);

    return (lds_ch455_handle_t)device;
}

int ldsCh455Deinit(lds_ch455_handle_t handle)
{
    lds_ch455_device_t *device = (lds_ch455_device_t *)handle;

    if (device == RT_NULL || !device->initialized) {
        return LDS_CH455_ERR_NOT_INIT;
    }

    rt_err_t result = rt_mutex_take(device->device_mutex, RT_WAITING_FOREVER);
    if (result != RT_EOK) {
        LOG_W("Failed to acquire device mutex for deinit");
    }

    /* Stop scanning thread */
    if (device->scan_active) {
        device->scan_active = false;

        if (device->scan_thread != RT_NULL) {
            /* Wait for thread to finish */
            rt_thread_mdelay(device->config.scan_interval_ms * 2);
            rt_thread_delete(device->scan_thread);
            device->scan_thread = RT_NULL;
        }

        LOG_I("Key scanning thread stopped");
    }

    /* Clear display */
    for (int i = 0; i < LDS_CH455_MAX_DIGITS; i++) {
        ldsCh455WriteDisplay(device, i, 0x00);
    }

    /* Put device to sleep */
    ldsCh455WriteCommand(device, LDS_CH455_SLEEPON);

    /* Unregister device */
    ldsCh455UnregisterDevice(device);

    /* Mark as uninitialized */
    device->initialized = false;

    if (result == RT_EOK) {
        rt_mutex_release(device->device_mutex);
    }

    /* Cleanup resources */
    rt_mutex_delete(device->device_mutex);
    rt_mutex_delete(device->i2c_mutex);
    rt_free(device);

    LOG_I("CH455 device deinitialized");
    return LDS_CH455_ERR_NONE;
}

int ldsCh455SetBrightness(lds_ch455_handle_t handle, uint8_t brightness)
{
    lds_ch455_device_t *device = (lds_ch455_device_t *)handle;

    if (device == RT_NULL || !device->initialized) {
        return LDS_CH455_ERR_NOT_INIT;
    }

    if (brightness > LDS_CH455_MAX_BRIGHTNESS) {
        return LDS_CH455_ERR_INVALID_PARAM;
    }

    rt_err_t result = rt_mutex_take(device->device_mutex,
                                   rt_tick_from_millisecond(device->config.i2c_timeout_ms));
    if (result != RT_EOK) {
        return LDS_CH455_ERR_TIMEOUT;
    }

    /* CH455 brightness command: system on with brightness setting */
    uint16_t brightness_cmd = LDS_CH455_SYSON;
    if (brightness == 8) {
        brightness_cmd |= LDS_CH455_BIT_INTENS8;
    } else if (brightness > 0) {
        brightness_cmd |= (brightness << 4);
    }
    int ret = ldsCh455WriteCommand(device, brightness_cmd);

    if (ret == LDS_CH455_ERR_NONE) {
        device->current_brightness = brightness;
        LOG_D("Brightness set to %d", brightness);
    }

    rt_mutex_release(device->device_mutex);
    return ret;
}

int ldsCh455DisplayDigit(lds_ch455_handle_t handle, uint8_t digit_pos, uint8_t value, bool decimal_point)
{
    lds_ch455_device_t *device = (lds_ch455_device_t *)handle;

    if (device == RT_NULL || !device->initialized) {
        return LDS_CH455_ERR_NOT_INIT;
    }

    if (digit_pos >= LDS_CH455_MAX_DIGITS) {
        return LDS_CH455_ERR_INVALID_PARAM;
    }

    /* Check if display functionality is enabled for current mode */
    if (device->config.mode == LDS_CH455_MODE_KEYBOARD_ONLY) {
        return LDS_CH455_ERR_INVALID_PARAM;  /* Display not available in keyboard-only mode */
    }

    rt_err_t result = rt_mutex_take(device->device_mutex,
                                   rt_tick_from_millisecond(device->config.i2c_timeout_ms));
    if (result != RT_EOK) {
        return LDS_CH455_ERR_TIMEOUT;
    }

    uint8_t segment_pattern;

    /* Get segment pattern for digit */
    if (value < sizeof(kDigitPatterns)) {
        segment_pattern = kDigitPatterns[value];
    } else {
        /* Custom pattern */
        segment_pattern = value;
    }

    /* Add decimal point if requested */
    if (decimal_point) {
        segment_pattern |= 0x80; /* DP bit */
    }

    /* Write to CH455 display */
    int ret = ldsCh455WriteDisplay(device, digit_pos, segment_pattern);

    if (ret == LDS_CH455_ERR_NONE) {
        device->display_buffer[digit_pos] = segment_pattern;
        LOG_D("Digit %d set to 0x%02X", digit_pos, segment_pattern);
    }

    rt_mutex_release(device->device_mutex);
    return ret;
}

int ldsCh455DisplayNumber(lds_ch455_handle_t handle, uint16_t number)
{
    lds_ch455_device_t *device = (lds_ch455_device_t *)handle;

    if (device == RT_NULL || !device->initialized) {
        return LDS_CH455_ERR_NOT_INIT;
    }

    if (number > 9999) {
        return LDS_CH455_ERR_INVALID_PARAM;
    }

    /* Check if display functionality is enabled for current mode */
    if (device->config.mode == LDS_CH455_MODE_KEYBOARD_ONLY) {
        return LDS_CH455_ERR_INVALID_PARAM;  /* Display not available in keyboard-only mode */
    }

    /* Extract digits */
    uint8_t digits[2];
    digits[0] = number % 10;           /* Units */
    digits[1] = (number / 10) % 10;    /* Tens */
    // digits[2] = (number / 100) % 10;   /* Hundreds */
    // digits[3] = (number / 1000) % 10;  /* Thousands */

    int ret = LDS_CH455_ERR_NONE;

    /* Display digits from right to left */
    for (int i = 0; i < LDS_CH455_MAX_DIGITS; i++) {
        uint8_t digit_value = digits[i];
        /* Clear digit */
        ret = ldsCh455DisplayDigit(handle, i, digit_value, false);
        if (ret != LDS_CH455_ERR_NONE) {
            break;
        }
        
    }

    if (ret == LDS_CH455_ERR_NONE) {
        LOG_D("Number %d displayed", number);
    }

    return ret;
}

int ldsCh455SetLed(lds_ch455_handle_t handle, uint8_t led_index, uint8_t pattern)
{
    lds_ch455_device_t *device = (lds_ch455_device_t *)handle;

    if (device == RT_NULL || !device->initialized) {
        return LDS_CH455_ERR_NOT_INIT;
    }

    if (led_index >= LDS_CH455_MAX_DIGITS) {
        return LDS_CH455_ERR_INVALID_PARAM;
    }

    /* Check if display functionality is enabled for current mode */
    if (device->config.mode == LDS_CH455_MODE_KEYBOARD_ONLY) {
        return LDS_CH455_ERR_INVALID_PARAM;  /* Display not available in keyboard-only mode */
    }

    rt_err_t result = rt_mutex_take(device->device_mutex,
                                   rt_tick_from_millisecond(device->config.i2c_timeout_ms));
    if (result != RT_EOK) {
        return LDS_CH455_ERR_TIMEOUT;
    }

    /* Write pattern directly to display */
    int ret = ldsCh455WriteDisplay(device, led_index, pattern);

    if (ret == LDS_CH455_ERR_NONE) {
        device->display_buffer[led_index] = pattern;
        LOG_D("LED %d set to pattern 0x%02X", led_index, pattern);
    }

    rt_mutex_release(device->device_mutex);
    return ret;
}

int ldsCh455ScanKeys(lds_ch455_handle_t handle, uint32_t *key_state)
{
    lds_ch455_device_t *device = (lds_ch455_device_t *)handle;

    if (device == RT_NULL || !device->initialized || key_state == RT_NULL) {
        return LDS_CH455_ERR_INVALID_PARAM;
    }

    /* Check if keyboard functionality is enabled for current mode */
    if (device->config.mode == LDS_CH455_MODE_DISPLAY_ONLY) {
        return LDS_CH455_ERR_INVALID_PARAM;  /* Keyboard not available in display-only mode */
    }

    rt_err_t result = rt_mutex_take(device->device_mutex,
                                   rt_tick_from_millisecond(device->config.i2c_timeout_ms));
    if (result != RT_EOK) {
        return LDS_CH455_ERR_TIMEOUT;
    }

    /* Read key data from CH455 */
    uint8_t key_data = 0;
    int ret = ldsCh455ReadKey(device, &key_data);

    if (ret == LDS_CH455_ERR_NONE) {
        /* Convert key code to bit mask */
        if (key_data != 0xFF) {  /* 0xFF means no key pressed */
            uint8_t key_code = key_data & 0x3F;  /* Extract key code (0-63) */
            if (key_code < LDS_CH455_MAX_KEYS) {
                *key_state = (1U << key_code);
            } else {
                *key_state = 0;
            }
        } else {
            *key_state = 0;  /* No key pressed */
        }

        device->stats.key_scan_count++;
        device->stats.last_scan_time = rt_tick_get();

        LOG_D("Key data: 0x%02X, Key state: 0x%05X", key_data, *key_state);
    }

    rt_mutex_release(device->device_mutex);
    return ret;
}

int ldsCh455GetStats(lds_ch455_handle_t handle, lds_ch455_stats_t *stats)
{
    lds_ch455_device_t *device = (lds_ch455_device_t *)handle;

    if (device == RT_NULL || !device->initialized || stats == RT_NULL) {
        return LDS_CH455_ERR_INVALID_PARAM;
    }

    rt_err_t result = rt_mutex_take(device->device_mutex,
                                   rt_tick_from_millisecond(device->config.i2c_timeout_ms));
    if (result != RT_EOK) {
        return LDS_CH455_ERR_TIMEOUT;
    }

    /* Copy statistics */
    rt_memcpy(stats, (void*)&device->stats, sizeof(lds_ch455_stats_t));

    rt_mutex_release(device->device_mutex);
    return LDS_CH455_ERR_NONE;
}

int ldsCh455ResetStats(lds_ch455_handle_t handle)
{
    lds_ch455_device_t *device = (lds_ch455_device_t *)handle;

    if (device == RT_NULL || !device->initialized) {
        return LDS_CH455_ERR_NOT_INIT;
    }

    rt_err_t result = rt_mutex_take(device->device_mutex,
                                   rt_tick_from_millisecond(device->config.i2c_timeout_ms));
    if (result != RT_EOK) {
        return LDS_CH455_ERR_TIMEOUT;
    }

    /* Reset statistics */
    rt_memset((void*)&device->stats, 0, sizeof(lds_ch455_stats_t));

    rt_mutex_release(device->device_mutex);

    LOG_I("CH455 statistics reset");
    return LDS_CH455_ERR_NONE;
}

int ldsCh455CheckHealth(lds_ch455_handle_t handle)
{
    lds_ch455_device_t *device = (lds_ch455_device_t *)handle;

    if (device == RT_NULL || !device->initialized) {
        return LDS_CH455_ERR_NOT_INIT;
    }

    rt_err_t result = rt_mutex_take(device->device_mutex,
                                   rt_tick_from_millisecond(device->config.i2c_timeout_ms));
    if (result != RT_EOK) {
        return LDS_CH455_ERR_TIMEOUT;
    }

    int health_status = 0;

    /* Check I2C communication health */
    if (device->stats.i2c_error_count > 0) {
        float error_rate = (float)device->stats.i2c_error_count /
                          (device->stats.i2c_tx_count + device->stats.i2c_rx_count + device->stats.i2c_error_count);

        if (error_rate > 0.1f) { /* >10% error rate */
            LOG_W("High I2C error rate: %.1f%%", error_rate * 100);
            health_status = 1; /* Warning */
        }

        if (error_rate > 0.5f) { /* >50% error rate */
            LOG_E("Critical I2C error rate: %.1f%%", error_rate * 100);
            health_status = -1; /* Error */
        }
    }

    /* Check if scanning thread is alive (if enabled) */
    if (device->config.auto_scan_enable && device->scan_active) {
        rt_tick_t time_since_scan = rt_tick_get() - device->stats.last_scan_time;
        uint32_t ms_since_scan = LDS_CH455_TICK_TO_MS(time_since_scan);

        if (ms_since_scan > (device->config.scan_interval_ms * 5)) {
            LOG_E("Key scanning appears stalled (last scan: %d ms ago)", ms_since_scan);
            health_status = -1; /* Error */
        }
    }

    /* Test basic communication */
    uint8_t test_data[1];
    int comm_result = ldsCh455ReadData(device, test_data, 1);
    if (comm_result != LDS_CH455_ERR_NONE) {
        LOG_E("Communication test failed: %d", comm_result);
        health_status = -1; /* Error */
    }

    rt_mutex_release(device->device_mutex);

    if (health_status == 0) {
        LOG_D("CH455 health check: OK");
    } else if (health_status > 0) {
        LOG_W("CH455 health check: Warning");
    } else {
        LOG_E("CH455 health check: Error");
    }

    return health_status;
}

int ldsCh455SetMode(lds_ch455_handle_t handle, lds_ch455_mode_t mode)
{
    lds_ch455_device_t *device = (lds_ch455_device_t *)handle;

    if (device == RT_NULL || !device->initialized) {
        return LDS_CH455_ERR_NOT_INIT;
    }

    if (mode >= 3) {  /* Invalid mode */
        return LDS_CH455_ERR_INVALID_PARAM;
    }

    rt_err_t result = rt_mutex_take(device->device_mutex,
                                   rt_tick_from_millisecond(device->config.i2c_timeout_ms));
    if (result != RT_EOK) {
        return LDS_CH455_ERR_TIMEOUT;
    }

    /* Stop current operations */
    bool was_scanning = device->scan_active;
    if (was_scanning) {
        device->scan_active = false;
        if (device->scan_thread != RT_NULL) {
            rt_thread_mdelay(device->config.scan_interval_ms * 2);  /* Wait for thread to stop */
        }
    }

    /* Update mode configuration */
    lds_ch455_mode_t old_mode = device->config.mode;
    device->config.mode = mode;

    int ret = LDS_CH455_ERR_NONE;

    /* Reconfigure hardware based on new mode */
    uint16_t system_cmd = LDS_CH455_SYSON;

    /* Add brightness setting */
    uint8_t brightness = device->current_brightness;
    if (brightness == 8) {
        system_cmd |= LDS_CH455_BIT_INTENS8;
    } else if (brightness > 0) {
        system_cmd |= (brightness << 4);
    }

    /* Apply system configuration */
    ret = ldsCh455WriteCommand(device, system_cmd);
    if (ret != LDS_CH455_ERR_NONE) {
        LOG_E("Failed to reconfigure CH455 for mode %d: %d", mode, ret);
        device->config.mode = old_mode;  /* Restore old mode */
        rt_mutex_release(device->device_mutex);
        return ret;
    }

    /* Clear display if display functionality is enabled */
    if (mode == LDS_CH455_MODE_DISPLAY_ONLY || mode == LDS_CH455_MODE_COMBINED) {
        for (int i = 0; i < LDS_CH455_MAX_DIGITS; i++) {
            ldsCh455WriteDisplay(device, i, 0x00);
            device->display_buffer[i] = 0x00;
        }
    }

    /* Restart key scanning if keyboard functionality is enabled */
    if ((mode == LDS_CH455_MODE_KEYBOARD_ONLY || mode == LDS_CH455_MODE_COMBINED) &&
        device->config.auto_scan_enable && was_scanning) {
        device->scan_active = true;
        /* Thread will restart automatically */
    }

    rt_mutex_release(device->device_mutex);

    LOG_I("CH455 mode changed from %d to %d", old_mode, mode);
    return LDS_CH455_ERR_NONE;
}

int ldsCh455GetMode(lds_ch455_handle_t handle, lds_ch455_mode_t *mode)
{
    lds_ch455_device_t *device = (lds_ch455_device_t *)handle;

    if (device == RT_NULL || !device->initialized || mode == RT_NULL) {
        return LDS_CH455_ERR_INVALID_PARAM;
    }

    *mode = device->config.mode;
    return LDS_CH455_ERR_NONE;
}

/* ================================ MSH Commands and Testing ================================ */

#if  defined(RT_USING_FINSH) && defined(LDS_CH455_ADD_TEST_CMD)
#include <finsh.h>

/**
 * @brief Print CH455 device status
 * @details Shows comprehensive status information for all CH455 devices
 *
 * @param handle Device handle (optional, 0 for all devices)
 */
static void ldsCh455PrintStatus(lds_ch455_handle_t handle)
{
    rt_kprintf("\n=== CH455 Device Status ===\n");

    if (handle != RT_NULL) {
        /* Print specific device status */
        lds_ch455_device_t *device = (lds_ch455_device_t *)handle;

        rt_kprintf("Device: 0x%08X\n", (uint32_t)device);
        rt_kprintf("I2C Bus: %s\n", device->config.i2c_bus_name);
        rt_kprintf("I2C Address: 0x%02X\n", device->config.i2c_addr);
        rt_kprintf("Initialized: %s\n", device->initialized ? "Yes" : "No");
        rt_kprintf("Scanning: %s\n", device->scan_active ? "Active" : "Inactive");
        rt_kprintf("Brightness: %d/%d\n", device->current_brightness, LDS_CH455_MAX_BRIGHTNESS);
        rt_kprintf("Mode: %d\n", device->config.mode);

        rt_kprintf("\n--- Statistics ---\n");
        rt_kprintf("I2C TX: %d\n", device->stats.i2c_tx_count);
        rt_kprintf("I2C RX: %d\n", device->stats.i2c_rx_count);
        rt_kprintf("I2C Errors: %d\n", device->stats.i2c_error_count);
        rt_kprintf("Key Presses: %d\n", device->stats.key_press_count);
        rt_kprintf("Key Scans: %d\n", device->stats.key_scan_count);
        rt_kprintf("Callbacks: %d\n", device->stats.callback_count);

        rt_tick_t time_since_scan = rt_tick_get() - device->stats.last_scan_time;
        rt_kprintf("Last Scan: %d ms ago\n", LDS_CH455_TICK_TO_MS(time_since_scan));

        rt_kprintf("\n--- Display Buffer ---\n");
        for (int i = 0; i < LDS_CH455_MAX_DIGITS; i++) {
            rt_kprintf("Digit %d: 0x%02X\n", i, device->display_buffer[i]);
        }
    } else {
        /* Print all devices */
        rt_mutex_take(g_instances_mutex, RT_WAITING_FOREVER);

        int active_count = 0;
        for (int i = 0; i < LDS_CH455_MAX_INSTANCES; i++) {
            if (g_ch455_devices[i] != RT_NULL) {
                rt_kprintf("Slot %d: Device 0x%08X\n", i, (uint32_t)g_ch455_devices[i]);
                active_count++;
            }
        }

        rt_mutex_release(g_instances_mutex);
        rt_kprintf("Active devices: %d/%d\n", active_count, LDS_CH455_MAX_INSTANCES);
    }

    rt_kprintf("========================\n\n");
}

/**
 * @brief Test key callback function
 * @details Callback function for key event testing
 */
static void ldsCh455TestKeyCallback(uint8_t key_code, lds_ch455_key_event_t event, void *user_data)
{
    const char *event_names[] = {"PRESSED", "RELEASED", "LONG_PRESSED"};
    rt_kprintf("Key %d %s\n", key_code, event_names[event]);
}

/**
 * @brief MSH command: ch455_init
 * @details Initialize CH455 device with default configuration
 */
static int cmd_ch455_init(int argc, char **argv)
{
    if (argc < 2) {
        rt_kprintf("Usage: ch455_init <i2c_bus_name> [i2c_addr]\n");
        rt_kprintf("Example: ch455_init i2c1 0x40\n");
        return -1;
    }

    /* Default configuration */
    lds_ch455_config_t config = {
        .i2c_bus_name = argv[1],
        .i2c_addr = LDS_CH455_I2C_ADDR_DEFAULT,
        .i2c_timeout_ms = LDS_CH455_I2C_TIMEOUT_MS,
        .i2c_retry_count = LDS_CH455_I2C_RETRY_COUNT,
        .mode = LDS_CH455_MODE_DISPLAY_ONLY,
        .brightness = 4,
        .scan_interval_ms = LDS_CH455_SCAN_INTERVAL_MS,
        .debounce_time_ms = LDS_CH455_DEBOUNCE_TIME_MS,
        .auto_scan_enable = true,
        .key_callback = ldsCh455TestKeyCallback,
        .user_data = RT_NULL,
    };

    /* Parse optional I2C address */
    if (argc >= 3) {
        config.i2c_addr = (uint8_t)strtol(argv[2], RT_NULL, 0);
    }

    lds_ch455_handle_t handle = ldsCh455Init(&config);
    if (handle == RT_NULL) {
        rt_kprintf("Failed to initialize CH455\n");
        return -1;
    }

    rt_kprintf("CH455 initialized: handle=0x%08X\n", (uint32_t)handle);
    return 0;
}

/**
 * @brief MSH command: ch455_status
 * @details Show CH455 device status
 */
static int cmd_ch455_status(int argc, char **argv)
{
    lds_ch455_handle_t handle = RT_NULL;

    if (argc >= 2) {
        handle = (lds_ch455_handle_t)strtol(argv[1], RT_NULL, 0);
    }

    ldsCh455PrintStatus(handle);
    return 0;
}

/**
 * @brief MSH command: ch455_brightness
 * @details Set CH455 brightness
 */
static int cmd_ch455_brightness(int argc, char **argv)
{
    if (argc < 3) {
        rt_kprintf("Usage: ch455_brightness <handle> <level>\n");
        rt_kprintf("Level: 1-8 (8=max)\n");
        return -1;
    }

    lds_ch455_handle_t handle = (lds_ch455_handle_t)strtol(argv[1], RT_NULL, 0);
    uint8_t brightness = (uint8_t)strtol(argv[2], RT_NULL, 0);

    int result = ldsCh455SetBrightness(handle, brightness);
    if (result == LDS_CH455_ERR_NONE) {
        rt_kprintf("Brightness set to %d\n", brightness);
    } else {
        rt_kprintf("Failed to set brightness: %d\n", result);
    }

    return result;
}

/**
 * @brief MSH command: ch455_display
 * @details Display number on CH455
 */
static int cmd_ch455_number(int argc, char **argv)
{
    if (argc < 3) {
        rt_kprintf("Usage: ch455_display <handle> <number> [leading_zeros]\n");
        rt_kprintf("Number: 0-9999\n");
        return -1;
    }

    lds_ch455_handle_t handle = (lds_ch455_handle_t)strtol(argv[1], RT_NULL, 0);
    uint16_t number = (uint16_t)strtol(argv[2], RT_NULL, 0);

    int result = ldsCh455DisplayNumber(handle, number);
    if (result == LDS_CH455_ERR_NONE) {
        rt_kprintf("Number %d displayed\n", number);
    } else {
        rt_kprintf("Failed to display number: %d\n", result);
    }

    return result;
}

static int cmd_ch455_display(int argc, char **argv)
{
    if (argc < 3) {
        rt_kprintf("Usage: ch455_display <handle> <number 0-F>\n");
        rt_kprintf("Number: 0-9999\n");
        return -1;
    }

    lds_ch455_handle_t handle = (lds_ch455_handle_t)strtol(argv[1], RT_NULL, 0);
    uint8_t number = (uint8_t)strtoul(argv[2], RT_NULL, 16);

    int result = ldsCh455DisplayDigit(handle, 0, number & 0x0F, true);
    result = ldsCh455DisplayDigit(handle, 1, number >> 4, true);
    if (result == LDS_CH455_ERR_NONE) {
        rt_kprintf("Number %02x displayed\n", number);
    } else {
        rt_kprintf("Failed to display number: %02x\n", result);
    }

    return result;
}

/**
 * @brief MSH command: ch455_health
 * @details Check CH455 device health
 */
static int cmd_ch455_health(int argc, char **argv)
{
    if (argc < 2) {
        rt_kprintf("Usage: ch455_health <handle>\n");
        return -1;
    }

    lds_ch455_handle_t handle = (lds_ch455_handle_t)strtol(argv[1], RT_NULL, 0);

    int health = ldsCh455CheckHealth(handle);
    rt_kprintf("Health status: %d ", health);

    if (health == 0) {
        rt_kprintf("(Healthy)\n");
    } else if (health > 0) {
        rt_kprintf("(Warning)\n");
    } else {
        rt_kprintf("(Error)\n");
    }

    return 0;
}

/**
 * @brief MSH command: ch455_test
 * @details Run comprehensive CH455 test
 */
static int cmd_ch455_test(int argc, char **argv)
{
    if (argc < 2) {
        rt_kprintf("Usage: ch455_test <handle>\n");
        return -1;
    }

    lds_ch455_handle_t handle = (lds_ch455_handle_t)strtol(argv[1], RT_NULL, 0);

    rt_kprintf("=== CH455 Comprehensive Test ===\n");

    int test_count = 0;
    int pass_count = 0;

    /* Test 1: Health check */
    test_count++;
    rt_kprintf("Test 1: Health check... ");
    int health = ldsCh455CheckHealth(handle);
    if (health >= 0) {
        rt_kprintf("PASS\n");
        pass_count++;
    } else {
        rt_kprintf("FAIL (health: %d)\n", health);
    }

    /* Test 2: Brightness control */
    test_count++;
    rt_kprintf("Test 2: Brightness control... ");
    int result = ldsCh455SetBrightness(handle, 8);
    if (result == LDS_CH455_ERR_NONE) {
        rt_thread_mdelay(100);
        result = ldsCh455SetBrightness(handle, 4);
        if (result == LDS_CH455_ERR_NONE) {
            rt_kprintf("PASS\n");
            pass_count++;
        } else {
            rt_kprintf("FAIL (brightness 2: %d)\n", result);
        }
    } else {
        rt_kprintf("FAIL (brightness 1: %d)\n", result);
    }

    /* Test 3: Display test */
    test_count++;
    rt_kprintf("Test 3: Display test... ");
    result = ldsCh455DisplayNumber(handle, 12);
    if (result == LDS_CH455_ERR_NONE) {
        rt_thread_mdelay(500);
        result = ldsCh455DisplayNumber(handle, 0);
        if (result == LDS_CH455_ERR_NONE) {
            rt_kprintf("PASS\n");
            pass_count++;
        } else {
            rt_kprintf("FAIL (clear: %d)\n", result);
        }
    } else {
        rt_kprintf("FAIL (display: %d)\n", result);
    }

    /* Test 4: Key scanning */
    test_count++;
    rt_kprintf("Test 4: Key scanning... ");
    uint32_t key_state;
    result = ldsCh455ScanKeys(handle, &key_state);
    if (result == LDS_CH455_ERR_NONE) {
        rt_kprintf("PASS (keys: 0x%05X)\n", key_state);
        pass_count++;
    } else {
        rt_kprintf("FAIL (scan: %d)\n", result);
    }

    /* Test 5: Statistics */
    test_count++;
    rt_kprintf("Test 5: Statistics... ");
    lds_ch455_stats_t stats;
    result = ldsCh455GetStats(handle, &stats);
    if (result == LDS_CH455_ERR_NONE) {
        rt_kprintf("PASS (TX: %d, RX: %d, Errors: %d)\n",
                   stats.i2c_tx_count, stats.i2c_rx_count, stats.i2c_error_count);
        pass_count++;
    } else {
        rt_kprintf("FAIL (stats: %d)\n", result);
    }

    rt_kprintf("\n=== Test Results: %d/%d tests passed ===\n", pass_count, test_count);

    if (pass_count == test_count) {
        rt_kprintf("🎉 All tests PASSED!\n");
        return 0;
    } else {
        rt_kprintf("❌ Some tests FAILED!\n");
        return -1;
    }
}

/* Register MSH commands */
MSH_CMD_EXPORT_ALIAS(cmd_ch455_init, ch455_init, Initialize CH455 device);
MSH_CMD_EXPORT_ALIAS(cmd_ch455_status, ch455_status, Show CH455 status);
MSH_CMD_EXPORT_ALIAS(cmd_ch455_brightness, ch455_brightness, Set CH455 brightness);
MSH_CMD_EXPORT_ALIAS(cmd_ch455_number, ch455_number, Display 10 number on CH455);
MSH_CMD_EXPORT_ALIAS(cmd_ch455_display, ch455_display, Display number on CH455);
MSH_CMD_EXPORT_ALIAS(cmd_ch455_health, ch455_health, Check CH455 health);
MSH_CMD_EXPORT_ALIAS(cmd_ch455_test, ch455_test, Run CH455 comprehensive test);

#endif /* RT_USING_FINSH */
