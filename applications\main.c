/*
 * Copyright (c) 2006-2023, RT-Thread Development Team
 *
 * SPDX-License-Identifier: Apache-2.0
 *
 * Change Logs:
 * Date           Author       Notes
 */
#include <stdint.h>
#include <string.h>
#include <rtthread.h>
#include <rtdevice.h>
#include <ulog.h>
#include "drv_spi.h"
#include "drv_sound.h"
#include "lds_utils.h"
#include "lds_key.h"
#include "lds_led_config.h"
#include "lds_uac.h"
#include "lds_digital_tube.h"
#include "lds_bk9535.h"
#include "lds_mic.h"
#include "lds_smart_base.h"
#include "lds_third_party.h"
#include "lds_dsp.h"
#include "lds_at.h"
#include <fal.h>

#ifdef PKG_USING_FLASHDB
#include "lds_kvdb.h"
#endif

#define FLASH_REGION_INDEX       0

#define POWER_CTRL_PIN_AMP      "PE.1"
#define POWER_CTRL_PIN_LINEOUT  "PB.1"

#define POWER_OFF_DETECT_PIN    "PD.3"

#define WDT_DEVICE_NAME    "wdt"    /* 看门狗设备名称 */
#define WDT_TIMEOUT         6       /* 看门狗超时时间 秒 */

static rt_device_t wdg_dev;         /* 看门狗设备句柄 */
static rt_base_t power_ctrl_amp = -1; /* 功放电源控制引脚 */
static rt_base_t power_ctrl_lineout = -1; /* lineout mute控制引脚 */
static bool powerOff = false;

static void idleHook(void)
{
    /* 在空闲线程的回调函数里喂狗 */
    rt_device_control(wdg_dev, RT_DEVICE_CTRL_WDT_KEEPALIVE, NULL);
    // rt_kprintf("feed the dog!\n ");
}
void ldsWdtFeed(void)
{
    /* 喂狗 */
    rt_device_control(wdg_dev, RT_DEVICE_CTRL_WDT_KEEPALIVE, NULL);
}
static int ldsWdtInit(void)
{
    // return RT_EOK;
    rt_err_t ret = RT_EOK;
    rt_uint32_t timeout = WDT_TIMEOUT;        /* 溢出时间，单位：秒 */

    /* 根据设备名称查找看门狗设备，获取设备句柄 */
    wdg_dev = rt_device_find(WDT_DEVICE_NAME);
    if (!wdg_dev)
    {
        LOG_E("find %s failed!\n", WDT_DEVICE_NAME);
        return -RT_ERROR;
    }
    /* 初始化设备 */
    rt_device_init(wdg_dev);
    /* 设置看门狗溢出时间 */
    ret = rt_device_control(wdg_dev, RT_DEVICE_CTRL_WDT_SET_TIMEOUT, &timeout);
    if (ret != RT_EOK)
    {
        LOG_E("set %s timeout failed!\n", WDT_DEVICE_NAME);
        return -RT_ERROR;
    }
    /* 启动看门狗 */
    ret = rt_device_control(wdg_dev, RT_DEVICE_CTRL_WDT_START, RT_NULL);
    if (ret != RT_EOK)
    {
        LOG_E("start %s failed!\n", WDT_DEVICE_NAME);
        return -RT_ERROR;
    }
    /* 设置空闲线程回调函数 */
    rt_thread_idle_sethook(idleHook);

    return ret;
}

/* 中断回调函数 */
void amp_mute(void *args)
{
    powerOff = true;
    rt_kprintf("turn off all output!\n");

    rt_pin_write(power_ctrl_amp, PIN_HIGH);
    rt_pin_write(power_ctrl_lineout, PIN_LOW);
    ldsBk9535SetCePin(false);
}
int ldsPoweroffDetectInit(void)
{
    rt_base_t pin = rt_pin_get(POWER_OFF_DETECT_PIN);
    if (pin < 0)
    {
        LOG_E("Get pin %s failed %d !", POWER_OFF_DETECT_PIN, pin);
        return -RT_ERROR;
    }

    rt_pin_mode(pin, PIN_MODE_INPUT);
    /* 绑定中断，下降沿模式，回调函数名为amp_mute */
    rt_pin_attach_irq(pin, PIN_IRQ_MODE_RISING, amp_mute, RT_NULL);
    rt_pin_irq_enable(pin, PIN_IRQ_ENABLE);
    return RT_EOK;
}
int main(void)
{
    uint32_t count = 0;
    uint32_t rasr = 0;
    uint8_t logLvl = 0;
    LOG_I("Starting %s version %s main process", MODEL_ID, APP_VERSION);

    //MPU设置
    ARM_MPU_Disable();
    rasr = ARM_MPU_RASR(0, ARM_MPU_AP_RO, 0, 0, 1, 0, 0, ARM_MPU_REGION_SIZE_256KB);
    ARM_MPU_SetRegion(ARM_MPU_RBAR(FLASH_REGION_INDEX, N32_FLASH_START_ADRESS), rasr);
    ARM_MPU_Enable(MPU_CTRL_PRIVDEFENA_Msk);

    ulog_flush(); // 刷新任何缓冲的日志消息
    ulog_async_output_enabled(false); // 初始化时禁用异步输出

    fal_init();
    //电源控制引脚初始化
    power_ctrl_amp = power_ctrl_pin_init(POWER_CTRL_PIN_AMP, PIN_LOW);
    power_ctrl_lineout = power_ctrl_pin_init(POWER_CTRL_PIN_LINEOUT, PIN_HIGH);

#ifdef PKG_USING_FLASHDB
    ldsKvdbInit();
#endif
    /* 初始化数码管 */
    ldsDigitalTubeInit();
    /* 初始化掉电检测 */
    ldsPoweroffDetectInit();
    /* 初始化LED */
    ldsLedInit();
    /* 初始化UAC通信 */
    ldsUacInit();
    /* 初始化DSP通信 */
    ldsDspInit();
    /* 初始化智能底座通信 */
    ldsSmartBaseInit();
    /* 初始化无感麦克风通信 */
    ldsMicInit();
    /* 初始化看门狗 */
    if(ldsWdtInit() != RT_EOK)
    {
        if(ldsWdtInit() != RT_EOK)
        {
            if(ldsWdtInit() != RT_EOK)
            {
                LOG_E("Watchdog init failed!, reboot system!");
                rt_thread_mdelay(1000);
                rt_hw_cpu_reset(); // 重启系统
            }
        }
    }
    /* 初始化UHF传输 */
    lds_bk9535_bk9529_init();
    /* 初始化按键 */
    ldsKeyInit();
    /* 初始化第三方通信 */
    ldsThirdPartyInit();
    /* 初始化AT命令服务 */
    ldsAtInit();

    ldsMicQueryVersion();
    ldsSmartBaseQueryVersion();

    logLvl = ldsAtLogSetted();
    LOG_I("log level is %d", logLvl);
    if(logLvl == 0){
        rt_console_set_device(RT_CONSOLE_DEVICE_NAME);
    } else {
        ulog_global_filter_lvl_set(logLvl);
    }
    ulog_async_output_enabled(true);

    while (1)
    {
        //防止掉电侦测抖动
        if(powerOff) {
            rt_thread_mdelay(600);
            powerOff = false;
            rt_pin_write(power_ctrl_amp, PIN_LOW);
            rt_pin_write(power_ctrl_lineout, PIN_HIGH);
        }
        // 10秒
        if(count % 10 == 0){
            ldsMicQueryStatus();
            ldsDspQueryVersion();
            lds_bk9535_prevent_rf_unlock();
            count = 0;
        }
        rt_thread_mdelay(1000);
        count++;
    }
}

#ifdef RT_USING_FINSH
static const char* log_lvl_strs[8] = {"assert", "null", "null", "error", "warn","null", "info", "debug"};
static int log_lvl_str_to_int(const char *str)
{
    for (int i = 0; i < 8; i++) {
        if (strcmp(str, log_lvl_strs[i]) == 0) {
            return i;
        }
    }
    return -1;
}
static void log_lvl(uint8_t argc, char **argv)
{
    int lvl = -1;
    if (argc == 3) {
        lvl = log_lvl_str_to_int(argv[2]);
        if(lvl == -1){
            rt_kprintf("Invalid log level %s.\n", argv[2]);
            return;
        }
        ulog_tag_lvl_filter_set(argv[1], lvl);
        rt_kprintf("Set tag %s log level to %s.\n", argv[1], argv[2]);
    } else if( argc == 2) {
        lvl = log_lvl_str_to_int(argv[1]);
        if(lvl == -1){
            rt_kprintf("Invalid log level %s.\n", argv[1]);
            return;
        }
        ulog_global_filter_lvl_set(lvl);
        rt_kprintf("Set global log level to %s.\n", argv[1]);
    } else {
        rt_kprintf("Please input: log_lvl <level> or log_lvl <tag> <level>.\n");
        rt_kprintf("assert  \n");
        rt_kprintf("error   \n");
        rt_kprintf("warn \n");
        rt_kprintf("info    \n");
        rt_kprintf("debug   \n");
    }
}
MSH_CMD_EXPORT(log_lvl, Set ulog filter level globally or by different tag .);
#endif