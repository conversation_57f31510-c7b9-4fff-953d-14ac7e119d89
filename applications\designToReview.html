<!DOCTYPE html><html><head>
      <title>designToReview</title>
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      
      <link rel="stylesheet" href="file:///c:\Users\<USER>\.vscode\extensions\shd101wyy.markdown-preview-enhanced-0.8.18\crossnote\dependencies\katex\katex.min.css">
      
      
      <script type="text/javascript" src="file:///c:\Users\<USER>\.vscode\extensions\shd101wyy.markdown-preview-enhanced-0.8.18\crossnote\dependencies\mermaid\mermaid.min.js" charset="UTF-8"></script>
      
      
      <style>
      code[class*=language-],pre[class*=language-]{color:#333;background:0 0;font-family:Consolas,"Liberation Mono",Menlo,Courier,monospace;text-align:left;white-space:pre;word-spacing:normal;word-break:normal;word-wrap:normal;line-height:1.4;-moz-tab-size:8;-o-tab-size:8;tab-size:8;-webkit-hyphens:none;-moz-hyphens:none;-ms-hyphens:none;hyphens:none}pre[class*=language-]{padding:.8em;overflow:auto;border-radius:3px;background:#f5f5f5}:not(pre)>code[class*=language-]{padding:.1em;border-radius:.3em;white-space:normal;background:#f5f5f5}.token.blockquote,.token.comment{color:#969896}.token.cdata{color:#183691}.token.doctype,.token.macro.property,.token.punctuation,.token.variable{color:#333}.token.builtin,.token.important,.token.keyword,.token.operator,.token.rule{color:#a71d5d}.token.attr-value,.token.regex,.token.string,.token.url{color:#183691}.token.atrule,.token.boolean,.token.code,.token.command,.token.constant,.token.entity,.token.number,.token.property,.token.symbol{color:#0086b3}.token.prolog,.token.selector,.token.tag{color:#63a35c}.token.attr-name,.token.class,.token.class-name,.token.function,.token.id,.token.namespace,.token.pseudo-class,.token.pseudo-element,.token.url-reference .token.variable{color:#795da3}.token.entity{cursor:help}.token.title,.token.title .token.punctuation{font-weight:700;color:#1d3e81}.token.list{color:#ed6a43}.token.inserted{background-color:#eaffea;color:#55a532}.token.deleted{background-color:#ffecec;color:#bd2c00}.token.bold{font-weight:700}.token.italic{font-style:italic}.language-json .token.property{color:#183691}.language-markup .token.tag .token.punctuation{color:#333}.language-css .token.function,code.language-css{color:#0086b3}.language-yaml .token.atrule{color:#63a35c}code.language-yaml{color:#183691}.language-ruby .token.function{color:#333}.language-markdown .token.url{color:#795da3}.language-makefile .token.symbol{color:#795da3}.language-makefile .token.variable{color:#183691}.language-makefile .token.builtin{color:#0086b3}.language-bash .token.keyword{color:#0086b3}pre[data-line]{position:relative;padding:1em 0 1em 3em}pre[data-line] .line-highlight-wrapper{position:absolute;top:0;left:0;background-color:transparent;display:block;width:100%}pre[data-line] .line-highlight{position:absolute;left:0;right:0;padding:inherit 0;margin-top:1em;background:hsla(24,20%,50%,.08);background:linear-gradient(to right,hsla(24,20%,50%,.1) 70%,hsla(24,20%,50%,0));pointer-events:none;line-height:inherit;white-space:pre}pre[data-line] .line-highlight:before,pre[data-line] .line-highlight[data-end]:after{content:attr(data-start);position:absolute;top:.4em;left:.6em;min-width:1em;padding:0 .5em;background-color:hsla(24,20%,50%,.4);color:#f4f1ef;font:bold 65%/1.5 sans-serif;text-align:center;vertical-align:.3em;border-radius:999px;text-shadow:none;box-shadow:0 1px #fff}pre[data-line] .line-highlight[data-end]:after{content:attr(data-end);top:auto;bottom:.4em}html body{font-family:'Helvetica Neue',Helvetica,'Segoe UI',Arial,freesans,sans-serif;font-size:16px;line-height:1.6;color:#333;background-color:#fff;overflow:initial;box-sizing:border-box;word-wrap:break-word}html body>:first-child{margin-top:0}html body h1,html body h2,html body h3,html body h4,html body h5,html body h6{line-height:1.2;margin-top:1em;margin-bottom:16px;color:#000}html body h1{font-size:2.25em;font-weight:300;padding-bottom:.3em}html body h2{font-size:1.75em;font-weight:400;padding-bottom:.3em}html body h3{font-size:1.5em;font-weight:500}html body h4{font-size:1.25em;font-weight:600}html body h5{font-size:1.1em;font-weight:600}html body h6{font-size:1em;font-weight:600}html body h1,html body h2,html body h3,html body h4,html body h5{font-weight:600}html body h5{font-size:1em}html body h6{color:#5c5c5c}html body strong{color:#000}html body del{color:#5c5c5c}html body a:not([href]){color:inherit;text-decoration:none}html body a{color:#08c;text-decoration:none}html body a:hover{color:#00a3f5;text-decoration:none}html body img{max-width:100%}html body>p{margin-top:0;margin-bottom:16px;word-wrap:break-word}html body>ol,html body>ul{margin-bottom:16px}html body ol,html body ul{padding-left:2em}html body ol.no-list,html body ul.no-list{padding:0;list-style-type:none}html body ol ol,html body ol ul,html body ul ol,html body ul ul{margin-top:0;margin-bottom:0}html body li{margin-bottom:0}html body li.task-list-item{list-style:none}html body li>p{margin-top:0;margin-bottom:0}html body .task-list-item-checkbox{margin:0 .2em .25em -1.8em;vertical-align:middle}html body .task-list-item-checkbox:hover{cursor:pointer}html body blockquote{margin:16px 0;font-size:inherit;padding:0 15px;color:#5c5c5c;background-color:#f0f0f0;border-left:4px solid #d6d6d6}html body blockquote>:first-child{margin-top:0}html body blockquote>:last-child{margin-bottom:0}html body hr{height:4px;margin:32px 0;background-color:#d6d6d6;border:0 none}html body table{margin:10px 0 15px 0;border-collapse:collapse;border-spacing:0;display:block;width:100%;overflow:auto;word-break:normal;word-break:keep-all}html body table th{font-weight:700;color:#000}html body table td,html body table th{border:1px solid #d6d6d6;padding:6px 13px}html body dl{padding:0}html body dl dt{padding:0;margin-top:16px;font-size:1em;font-style:italic;font-weight:700}html body dl dd{padding:0 16px;margin-bottom:16px}html body code{font-family:Menlo,Monaco,Consolas,'Courier New',monospace;font-size:.85em;color:#000;background-color:#f0f0f0;border-radius:3px;padding:.2em 0}html body code::after,html body code::before{letter-spacing:-.2em;content:'\00a0'}html body pre>code{padding:0;margin:0;word-break:normal;white-space:pre;background:0 0;border:0}html body .highlight{margin-bottom:16px}html body .highlight pre,html body pre{padding:1em;overflow:auto;line-height:1.45;border:#d6d6d6;border-radius:3px}html body .highlight pre{margin-bottom:0;word-break:normal}html body pre code,html body pre tt{display:inline;max-width:initial;padding:0;margin:0;overflow:initial;line-height:inherit;word-wrap:normal;background-color:transparent;border:0}html body pre code:after,html body pre code:before,html body pre tt:after,html body pre tt:before{content:normal}html body blockquote,html body dl,html body ol,html body p,html body pre,html body ul{margin-top:0;margin-bottom:16px}html body kbd{color:#000;border:1px solid #d6d6d6;border-bottom:2px solid #c7c7c7;padding:2px 4px;background-color:#f0f0f0;border-radius:3px}@media print{html body{background-color:#fff}html body h1,html body h2,html body h3,html body h4,html body h5,html body h6{color:#000;page-break-after:avoid}html body blockquote{color:#5c5c5c}html body pre{page-break-inside:avoid}html body table{display:table}html body img{display:block;max-width:100%;max-height:100%}html body code,html body pre{word-wrap:break-word;white-space:pre}}.markdown-preview{width:100%;height:100%;box-sizing:border-box}.markdown-preview ul{list-style:disc}.markdown-preview ul ul{list-style:circle}.markdown-preview ul ul ul{list-style:square}.markdown-preview ol{list-style:decimal}.markdown-preview ol ol,.markdown-preview ul ol{list-style-type:lower-roman}.markdown-preview ol ol ol,.markdown-preview ol ul ol,.markdown-preview ul ol ol,.markdown-preview ul ul ol{list-style-type:lower-alpha}.markdown-preview .newpage,.markdown-preview .pagebreak{page-break-before:always}.markdown-preview pre.line-numbers{position:relative;padding-left:3.8em;counter-reset:linenumber}.markdown-preview pre.line-numbers>code{position:relative}.markdown-preview pre.line-numbers .line-numbers-rows{position:absolute;pointer-events:none;top:1em;font-size:100%;left:0;width:3em;letter-spacing:-1px;border-right:1px solid #999;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}.markdown-preview pre.line-numbers .line-numbers-rows>span{pointer-events:none;display:block;counter-increment:linenumber}.markdown-preview pre.line-numbers .line-numbers-rows>span:before{content:counter(linenumber);color:#999;display:block;padding-right:.8em;text-align:right}.markdown-preview .mathjax-exps .MathJax_Display{text-align:center!important}.markdown-preview:not([data-for=preview]) .code-chunk .code-chunk-btn-group{display:none}.markdown-preview:not([data-for=preview]) .code-chunk .status{display:none}.markdown-preview:not([data-for=preview]) .code-chunk .output-div{margin-bottom:16px}.markdown-preview .md-toc{padding:0}.markdown-preview .md-toc .md-toc-link-wrapper .md-toc-link{display:inline;padding:.25rem 0}.markdown-preview .md-toc .md-toc-link-wrapper .md-toc-link div,.markdown-preview .md-toc .md-toc-link-wrapper .md-toc-link p{display:inline}.markdown-preview .md-toc .md-toc-link-wrapper.highlighted .md-toc-link{font-weight:800}.scrollbar-style::-webkit-scrollbar{width:8px}.scrollbar-style::-webkit-scrollbar-track{border-radius:10px;background-color:transparent}.scrollbar-style::-webkit-scrollbar-thumb{border-radius:5px;background-color:rgba(150,150,150,.66);border:4px solid rgba(150,150,150,.66);background-clip:content-box}html body[for=html-export]:not([data-presentation-mode]){position:relative;width:100%;height:100%;top:0;left:0;margin:0;padding:0;overflow:auto}html body[for=html-export]:not([data-presentation-mode]) .markdown-preview{position:relative;top:0;min-height:100vh}@media screen and (min-width:914px){html body[for=html-export]:not([data-presentation-mode]) .markdown-preview{padding:2em calc(50% - 457px + 2em)}}@media screen and (max-width:914px){html body[for=html-export]:not([data-presentation-mode]) .markdown-preview{padding:2em}}@media screen and (max-width:450px){html body[for=html-export]:not([data-presentation-mode]) .markdown-preview{font-size:14px!important;padding:1em}}@media print{html body[for=html-export]:not([data-presentation-mode]) #sidebar-toc-btn{display:none}}html body[for=html-export]:not([data-presentation-mode]) #sidebar-toc-btn{position:fixed;bottom:8px;left:8px;font-size:28px;cursor:pointer;color:inherit;z-index:99;width:32px;text-align:center;opacity:.4}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] #sidebar-toc-btn{opacity:1}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc{position:fixed;top:0;left:0;width:300px;height:100%;padding:32px 0 48px 0;font-size:14px;box-shadow:0 0 4px rgba(150,150,150,.33);box-sizing:border-box;overflow:auto;background-color:inherit}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc::-webkit-scrollbar{width:8px}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc::-webkit-scrollbar-track{border-radius:10px;background-color:transparent}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc::-webkit-scrollbar-thumb{border-radius:5px;background-color:rgba(150,150,150,.66);border:4px solid rgba(150,150,150,.66);background-clip:content-box}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc a{text-decoration:none}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc .md-toc{padding:0 16px}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc .md-toc .md-toc-link-wrapper .md-toc-link{display:inline;padding:.25rem 0}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc .md-toc .md-toc-link-wrapper .md-toc-link div,html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc .md-toc .md-toc-link-wrapper .md-toc-link p{display:inline}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc .md-toc .md-toc-link-wrapper.highlighted .md-toc-link{font-weight:800}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .markdown-preview{left:300px;width:calc(100% - 300px);padding:2em calc(50% - 457px - 300px / 2);margin:0;box-sizing:border-box}@media screen and (max-width:1274px){html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .markdown-preview{padding:2em}}@media screen and (max-width:450px){html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .markdown-preview{width:100%}}html body[for=html-export]:not([data-presentation-mode]):not([html-show-sidebar-toc]) .markdown-preview{left:50%;transform:translateX(-50%)}html body[for=html-export]:not([data-presentation-mode]):not([html-show-sidebar-toc]) .md-sidebar-toc{display:none}
/* Please visit the URL below for more information: */
/*   https://shd101wyy.github.io/markdown-preview-enhanced/#/customize-css */

      </style>
      <!-- The content below will be included at the end of the <head> element. --><script type="text/javascript">
  document.addEventListener("DOMContentLoaded", function () {
    // your code here
  });
</script></head><body for="html-export">
    
    
      <div class="crossnote markdown-preview  ">
      
<h2 id="扩音主机固件详细设计">扩音主机固件详细设计 </h2>
<p>本文档用于固件代码评审会议，基于 applications 目录源码（排除 usbd_app.c、lds_bk9532*、lds_kvdb*）。内容包含架构、模块分析、数据流与状态机、接口规格、内存/错误/并发设计与评审关注点。所有示意图使用 Mermaid。</p>
<hr>
<h2 id="架构总览">架构总览 </h2>
<ul>
<li>OS/驱动层：RT-Thread（线程、定时器、消息队列、PIN、I2C、UART、ulog 等）</li>
<li>公共基础：lds_utils（校验/位反转/电源管脚初始化）、lds_uart（统一串口接收队列+回调分发）</li>
<li>外设驱动：
<ul>
<li>LED 指示（lds_led + lds_led_config）</li>
<li>数码管/按键芯片 CH455（I2C，lds_ch455 + lds_digital_tube）</li>
<li>UHF 发射芯片 BK9535（I2C，lds_bk9535）</li>
</ul>
</li>
<li>协议与业务模块（基于 UART）：
<ul>
<li>UAC 控制（lds_uac，固定 6 字节帧）</li>
<li>麦克风阵列（lds_mic，带序列号/校验/重传队列）</li>
<li>DSP 效果（lds_dsp，简单帧/ACK/重传队列）</li>
<li>智能底座（lds_smart_base，CRC16/序列/ACK/应答）</li>
<li>第三方主机桥接（lds_third_party，向上游提供统一控制与查询）</li>
</ul>
</li>
<li>输入与系统：按键与拨码（lds_button/lds_key/lds_multiple_pin_switch），入口 main.c（初始化、喂狗、掉电检测）</li>
</ul>
<div class="mermaid">graph TD

  A[main.c] --&gt;|初始化顺序| LED[lds_led+config]
  A --&gt; UAC[lds_uac]
  A --&gt; DSP[lds_dsp]
  A --&gt; BASE[lds_smart_base]
  A --&gt; MIC[lds_mic]
  A --&gt; BK[lds_bk9535]
  A --&gt; KEY[lds_key]
  A --&gt; TP[lds_third_party]
  KEY --&gt; DSP
  KEY --&gt; MIC
  KEY --&gt; BK
  KEY --&gt; DT[lds_digital_tube]
  DT --&gt; CH455
  UAC &amp; DSP &amp; BASE &amp; MIC &amp; TP --&gt; UART[lds_uart]
  CH455 --&gt; I2C
  BK --&gt; I2C
  LED --&gt; GPIO
  KEY --&gt; GPIO
</div><hr>
<h2 id="模块分析逐文件">模块分析（逐文件） </h2>
<h3 id="mainc系统入口">main.c（系统入口） </h3>
<ul>
<li>功能：MPU 配置、FAL、管脚上电、模块初始化顺序、喂狗、掉电中断、周期查询（MIC/DSP 版本/状态与 RF 解锁防护）</li>
<li>关键初始化顺序：LED → UAC → DSP → SmartBase → MIC → WDT → BK9535 → Key → ThirdParty → AT</li>
<li>低功耗/掉电影响：POWER_OFF_DETECT_PIN 上升沿中断置标志，主循环延迟 600ms 去抖后关闭功放/LineOut</li>
<li>关键代码：</li>
</ul>
<pre data-role="codeBlock" data-info="c" class="language-c c"><code><span class="token comment">/* init order */</span>
<span class="token function">ldsLedInit</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">;</span> 
<span class="token function">ldsUacInit</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">;</span> 
<span class="token function">ldsDspInit</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">;</span> 
<span class="token function">ldsSmartBaseInit</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
<span class="token function">ldsMicInit</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">;</span> 
<span class="token function">lds_bk9535_bk9529_init</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">;</span> 
<span class="token function">ldsKeyInit</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
<span class="token function">ldsThirdPartyInit</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">;</span> 
<span class="token function">ldsAtInit</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
</code></pre><h3 id="lds_uartch统一-uart-框架">lds_uart.c/.h（统一 UART 框架） </h3>
<ul>
<li>作用：
<ul>
<li>DMA 接收中断 -&gt; rx_mq 消息队列 -&gt; 后台线程读取 -&gt; 调用每路 UART 的注册回调</li>
<li>提供 ldsUartInit(uart_name, index, cb) 与回调注册</li>
</ul>
</li>
<li>并发：消息队列 + 单后台线程；每个模块解析在各自回调上下文，不再阻塞中断</li>
<li>关键代码：</li>
</ul>
<pre data-role="codeBlock" data-info="c" class="language-c c"><code><span class="token keyword keyword-static">static</span> <span class="token class-name">rt_err_t</span> <span class="token function">ldsUartIsrCallback</span><span class="token punctuation">(</span><span class="token class-name">rt_device_t</span> dev<span class="token punctuation">,</span> <span class="token class-name">rt_size_t</span> size<span class="token punctuation">)</span><span class="token punctuation">{</span>
  <span class="token keyword keyword-struct">struct</span> <span class="token class-name">rx_msg</span> msg<span class="token operator">=</span><span class="token punctuation">{</span><span class="token punctuation">.</span>dev<span class="token operator">=</span>dev<span class="token punctuation">,</span><span class="token punctuation">.</span>size<span class="token operator">=</span>size<span class="token punctuation">}</span><span class="token punctuation">;</span>
  <span class="token keyword keyword-return">return</span> <span class="token function">rt_mq_send</span><span class="token punctuation">(</span><span class="token operator">&amp;</span>rx_mq<span class="token punctuation">,</span> <span class="token operator">&amp;</span>msg<span class="token punctuation">,</span> <span class="token keyword keyword-sizeof">sizeof</span><span class="token punctuation">(</span>msg<span class="token punctuation">)</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
<span class="token punctuation">}</span>
</code></pre><h3 id="lds_ledch-与-lds_led_configcled-管理">lds_led.c/.h 与 lds_led_config.c（LED 管理） </h3>
<ul>
<li>LED 驱动线程每 20ms 周期执行，支持常亮/常灭/闪烁/特序列；config 统一绑定产品脚位并暴露逻辑索引</li>
<li>并发：互斥锁保护配置；独立线程执行闪烁时序</li>
<li>交互：被 MIC/KEY/第三方模块调用更新指示</li>
</ul>
<h3 id="lds_ch455ch-与-lds_digital_tubecch455-驱动数码管">lds_ch455.c/.h 与 lds_digital_tube.c（CH455 驱动＋数码管） </h3>
<ul>
<li>CH455：I2C 协议写 16bit 命令、读键值，支持自动扫描线程与亮度设置、统计/健康检查</li>
<li>数码管：以 CH455 显示接口封装数字/十六进制显示</li>
<li>并发：设备互斥锁、I2C 互斥锁、可选扫描线程</li>
<li>关键代码：</li>
</ul>
<pre data-role="codeBlock" data-info="c" class="language-c c"><code><span class="token keyword keyword-static">static</span> <span class="token keyword keyword-void">void</span> <span class="token function">ldsCh455ScanThread</span><span class="token punctuation">(</span><span class="token keyword keyword-void">void</span> <span class="token operator">*</span>parameter<span class="token punctuation">)</span><span class="token punctuation">{</span>
  <span class="token keyword keyword-while">while</span> <span class="token punctuation">(</span>device<span class="token operator">-&gt;</span>scan_active<span class="token punctuation">)</span><span class="token punctuation">{</span>
    <span class="token function">ldsCh455ReadKey</span><span class="token punctuation">(</span>device<span class="token punctuation">,</span><span class="token operator">&amp;</span>key_data<span class="token punctuation">)</span><span class="token punctuation">;</span> <span class="token comment">// 转换/去抖/回调</span>
    <span class="token function">rt_thread_mdelay</span><span class="token punctuation">(</span>device<span class="token operator">-&gt;</span>config<span class="token punctuation">.</span>scan_interval_ms<span class="token punctuation">)</span><span class="token punctuation">;</span>
  <span class="token punctuation">}</span>
<span class="token punctuation">}</span>
</code></pre><h3 id="lds_bk9535chuhf-发射芯片">lds_bk9535.c/.h（UHF 发射芯片） </h3>
<ul>
<li>功能：I2C 寄存器表初始化、设频、功率、RF 解锁检测恢复</li>
<li>可靠性：I2C 指数退避重试、连续失败阈值触发硬件复位+重新配置</li>
<li>关键代码：</li>
</ul>
<pre data-role="codeBlock" data-info="c" class="language-c c"><code><span class="token keyword keyword-static">static</span> <span class="token class-name">uint32_t</span> <span class="token function">ldsBk9535CalculateRetryDelay</span><span class="token punctuation">(</span><span class="token class-name">uint8_t</span> retry<span class="token punctuation">)</span><span class="token punctuation">{</span>
  <span class="token class-name">uint32_t</span> d <span class="token operator">=</span> base<span class="token punctuation">;</span> <span class="token keyword keyword-while">while</span><span class="token punctuation">(</span>retry<span class="token operator">--</span> <span class="token operator">&amp;&amp;</span> d<span class="token operator">&lt;</span>max<span class="token punctuation">)</span> d<span class="token operator">&lt;&lt;=</span><span class="token number">1</span><span class="token punctuation">;</span> <span class="token keyword keyword-return">return</span> d<span class="token operator">&gt;</span>max<span class="token operator">?</span>max<span class="token operator">:</span>d<span class="token punctuation">;</span>
<span class="token punctuation">}</span>
</code></pre><h3 id="lds_uacchuac-控制">lds_uac.c/.h（UAC 控制） </h3>
<ul>
<li>帧格式：固定 6 字节（0x55 0xAA + LEN + CMD + DATA + XOR）</li>
<li>状态机：HEAD1→HEAD2→LEN→CMD→VAL→CRC，心跳超时重置 UAC</li>
<li>用途：向上位机/USB 发送按键（PPT）上翻页/下翻页</li>
</ul>
<h3 id="lds_micch麦克风阵列协议">lds_mic.c/.h（麦克风阵列协议） </h3>
<ul>
<li>帧格式：A5 + modelId(2) + CMD(2) + SEQ + Addr + LEN(2) + DATA + SUM</li>
<li>能力：命令发送队列（序列号/超时重传/最大失败熔断禁用）、心跳定时器、解析定时器</li>
<li>业务：静音/声效模式/查询版本与状态，联动 LED</li>
<li>关键代码：</li>
</ul>
<pre data-role="codeBlock" data-info="c" class="language-c c"><code><span class="token keyword keyword-static">static</span> <span class="token keyword keyword-int">int</span> <span class="token function">ldsMicSendCommand</span><span class="token punctuation">(</span><span class="token punctuation">.</span><span class="token punctuation">.</span><span class="token punctuation">.</span><span class="token punctuation">)</span><span class="token punctuation">{</span>
  <span class="token keyword keyword-int">int</span> slot<span class="token operator">=</span><span class="token function">find_free</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">;</span> <span class="token keyword keyword-int">int</span> seq<span class="token operator">=</span><span class="token function">ldsMicSendFrame</span><span class="token punctuation">(</span><span class="token punctuation">.</span><span class="token punctuation">.</span><span class="token punctuation">.</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
  queue<span class="token punctuation">[</span>slot<span class="token punctuation">]</span><span class="token operator">=</span><span class="token punctuation">{</span><span class="token punctuation">.</span>active<span class="token operator">=</span>true<span class="token punctuation">,</span><span class="token punctuation">.</span>seq<span class="token operator">=</span>seq<span class="token punctuation">,</span><span class="token punctuation">.</span><span class="token punctuation">.</span><span class="token punctuation">.</span><span class="token punctuation">}</span><span class="token punctuation">;</span> <span class="token function">start_retry_timer_if_needed</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
<span class="token punctuation">}</span>
</code></pre><h3 id="lds_dspchdsp-芯片协议">lds_dsp.c/.h（DSP 芯片协议） </h3>
<ul>
<li>帧格式：A5 5A + CTRL + LEN + DATA + 16</li>
<li>能力：命令队列 + 单字节 CTRL 的 ACK 匹配（收到同 CTRL 视作应答），超时重发</li>
<li>业务：音量/高低音/LineIn 倍率/LineSelect、版本查询</li>
</ul>
<h3 id="lds_smart_basech智能底座协议">lds_smart_base.c/.h（智能底座协议） </h3>
<ul>
<li>帧格式：5A A5 + CMD + SEQ + LEN(2) + DATA + CRC16</li>
<li>能力：命令队列/序列号/ACK 匹配/自动应答，心跳与解析超时</li>
<li>业务：
<ul>
<li>KEY：透传 PPT 按键至 UAC</li>
<li>STATUS：连接与是否有声音活动；联动 DSP 的 LineSelect（闪避）</li>
<li>VERSION：字符串版本回传</li>
</ul>
</li>
</ul>
<h3 id="lds_third_partych第三方对接">lds_third_party.c/.h（第三方对接） </h3>
<ul>
<li>作为上游主机指令入口：静音/声效/电源控制/版本/状态查询；内部调用 MIC/BASE/LED 与 GPIO</li>
<li>同样具备序列/CRC/队列/应答机制</li>
<li>关键代码：</li>
</ul>
<pre data-role="codeBlock" data-info="c" class="language-c c"><code><span class="token keyword keyword-case">case</span> LDS_THIRD_PARTY_CMD_POWER_CTRL<span class="token operator">:</span>
  <span class="token keyword keyword-if">if</span><span class="token punctuation">(</span>dev<span class="token operator">==</span><span class="token number">0</span><span class="token punctuation">)</span> 
    <span class="token function">ldsMicSetPowerCtrl</span><span class="token punctuation">(</span>on<span class="token punctuation">)</span><span class="token punctuation">;</span> 
  <span class="token keyword keyword-else">else</span> 
    <span class="token function">rt_pin_write</span><span class="token punctuation">(</span>g_powerCtrlUsb<span class="token punctuation">,</span>on<span class="token punctuation">)</span><span class="token punctuation">;</span>
</code></pre><h3 id="lds_keyc按键与多位拨码">lds_key.c（按键与多位拨码） </h3>
<ul>
<li>轮询 GPIO 与多位拨码（组合二进制），带 2 次滤波计数；</li>
<li>动作：
<ul>
<li>GPIO：设置 LineIn 倍率、UHF 发射功率、闪避模式</li>
<li>多位拨码：音量/高音/低音/UHF 频道（驱动 BK9535 写频并在数码管显示）</li>
</ul>
</li>
<li>并发：独立“lds_btn”线程 20ms 周期扫描</li>
</ul>
<h3 id="lds_buttonch通用按键库">lds_button.c/.h（通用按键库） </h3>
<ul>
<li>事件：单击/双击/连击/短按/长按/长按保持，需配合 lds_key 绑定 GPIO 读取</li>
</ul>
<h3 id="lds_utilsch工具">lds_utils.c/.h（工具） </h3>
<ul>
<li>XOR/SUM/CRC16/CRC32 校验；位反转；管脚初始化；CRC16 使用片上 CRC 外设+互斥保护</li>
</ul>
<h3 id="lds_atch工厂调试-at">lds_at.c/.h（工厂/调试 AT） </h3>
<ul>
<li>命令：进出工测、查询 FW/HW/日期、重启、MCU OTA（ymodem）、日志等级</li>
<li>与主流程：main 中初始化；部分模块在工测模式抑制业务通信</li>
</ul>
<hr>
<h2 id="数据流与交互">数据流与交互 </h2>
<h3 id="uart-接收路径统一">UART 接收路径（统一） </h3>
<div class="mermaid">sequenceDiagram
  participant ISR as UART RX ISR
  participant MQ as rx_mq
  participant TH as ldsUartThread
  participant MOD as 模块回调(MIC/DSP/BASE/UAC/ThrdParty)
  ISR-&gt;&gt;MQ: rt_mq_send(rx_msg)
  TH-&gt;&gt;MQ: rt_mq_recv()
  TH-&gt;&gt;TH: rt_device_read()
  TH-&gt;&gt;MOD: cb(dev, buf, len)
  MOD-&gt;&gt;MOD: 解析状态机/ACK/重传
</div><h3 id="按键拨码--业务联动">按键/拨码 → 业务联动 </h3>
<div class="mermaid">sequenceDiagram
  participant Key as lds_key
  participant DSP as lds_dsp
  participant MIC as lds_mic
  participant BK as lds_bk9535
  participant DT as lds_digital_tube
  Key-&gt;&gt;DSP: 设置音量/高音/低音/LineIn倍数
  Key-&gt;&gt;MIC: 设置主/从麦静音/声效
  Key-&gt;&gt;BK: 选择频道并设频
  BK--&gt;&gt;DT: 显示频道号
</div><h3 id="智能底座闪避联动">智能底座闪避联动 </h3>
<div class="mermaid">sequenceDiagram
  participant BASE as lds_smart_base
  participant DSP as lds_dsp
  BASE-&gt;&gt;BASE: STATUS 帧（connect, active）
  BASE-&gt;&gt;DSP: ldsDspSetLineSelect(active)
</div><hr>
<h2 id="协议解析状态机">协议解析状态机 </h2>
<h3 id="mic">MIC </h3>
<div class="mermaid">stateDiagram-v2
  [*] --&gt; IDLE
  IDLE --&gt; MODEL_H: 0xA5
  MODEL_H --&gt; MODEL_L
  MODEL_L --&gt; CMD_H
  CMD_H --&gt; CMD_L
  CMD_L --&gt; SEQ
  SEQ --&gt; ADDR
  ADDR --&gt; LEN_H
  LEN_H --&gt; LEN_L
  LEN_L --&gt; DATA: len&gt;0
  LEN_L --&gt; SUM: len==0
  DATA --&gt; SUM
  SUM --&gt; IDLE: 校验通过/失败复位
</div><h3 id="dsp">DSP </h3>
<div class="mermaid">stateDiagram-v2
  [*] --&gt; IDLE
  IDLE --&gt; HEAD
  HEAD --&gt; CMD
  CMD --&gt; LEN
  LEN --&gt; DATA
  DATA --&gt; END: 0x16
  END --&gt; IDLE
</div><h3 id="smart_base--third_party相似">SMART_BASE / THIRD_PARTY（相似） </h3>
<div class="mermaid">stateDiagram-v2
  [*] --&gt; H1
  H1 --&gt; H2
  H2 --&gt; CMD
  CMD --&gt; SEQ
  SEQ --&gt; LEN_H
  LEN_H --&gt; LEN_L
  LEN_L --&gt; DATA
  DATA --&gt; CRC_H
  CRC_H --&gt; CRC_L
  CRC_L --&gt; H1: CRC OK/复位
</div><h3 id="uac6-字节定长">UAC（6 字节定长） </h3>
<div class="mermaid">stateDiagram-v2
  [*] --&gt; H1
  H1 --&gt; H2
  H2 --&gt; LEN
  LEN --&gt; CMD
  CMD --&gt; VAL
  VAL --&gt; CRC
  CRC --&gt; H1: 校验通过/失败复位
</div><hr>
<h2 id="接口规格api-摘要">接口规格（API 摘要） </h2>
<ul>
<li>UART 框架：</li>
</ul>
<pre data-role="codeBlock" data-info="c" class="language-c c"><code><span class="token class-name">rt_device_t</span> <span class="token function">ldsUartInit</span><span class="token punctuation">(</span><span class="token keyword keyword-const">const</span> <span class="token keyword keyword-char">char</span><span class="token operator">*</span> name<span class="token punctuation">,</span> LDS_UART_INDEX_E idx<span class="token punctuation">,</span> <span class="token class-name">ldsUartCb_t</span> cb<span class="token punctuation">)</span><span class="token punctuation">;</span>
</code></pre><ul>
<li>MIC：</li>
</ul>
<pre data-role="codeBlock" data-info="c" class="language-c c"><code><span class="token keyword keyword-int">int</span> <span class="token function">ldsMicSetMuteControl</span><span class="token punctuation">(</span><span class="token class-name">uint8_t</span> addr<span class="token punctuation">,</span> LDS_MIC_MUTE_E mute<span class="token punctuation">)</span><span class="token punctuation">;</span>
<span class="token keyword keyword-int">int</span> <span class="token function">ldsMicSetSoundMode</span><span class="token punctuation">(</span>LDS_MIC_SOUND_MODE_E mode<span class="token punctuation">)</span><span class="token punctuation">;</span>
</code></pre><ul>
<li>DSP：</li>
</ul>
<pre data-role="codeBlock" data-info="c" class="language-c c"><code><span class="token keyword keyword-int">int</span> <span class="token function">ldsDspSetVolume</span><span class="token punctuation">(</span><span class="token class-name">uint8_t</span> vol<span class="token punctuation">)</span><span class="token punctuation">;</span>
<span class="token keyword keyword-int">int</span> <span class="token function">ldsDspSetTreble</span><span class="token punctuation">(</span><span class="token class-name">uint8_t</span> treble<span class="token punctuation">)</span><span class="token punctuation">;</span>
<span class="token keyword keyword-int">int</span> <span class="token function">ldsDspSetBass</span><span class="token punctuation">(</span><span class="token class-name">uint8_t</span> bass<span class="token punctuation">)</span><span class="token punctuation">;</span>
</code></pre><ul>
<li>Smart Base：</li>
</ul>
<pre data-role="codeBlock" data-info="c" class="language-c c"><code><span class="token keyword keyword-int">int</span> <span class="token function">ldsSmartBaseQueryStatus</span><span class="token punctuation">(</span><span class="token keyword keyword-void">void</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
<span class="token keyword keyword-int">int</span> <span class="token function">ldsSmartBaseSetSelectMode</span><span class="token punctuation">(</span><span class="token class-name">int8_t</span> mode<span class="token punctuation">)</span><span class="token punctuation">;</span>
</code></pre><ul>
<li>UAC：</li>
</ul>
<pre data-role="codeBlock" data-info="c" class="language-c c"><code><span class="token keyword keyword-void">void</span> <span class="token function">ldsUacKeyCmdsend</span><span class="token punctuation">(</span>bool key_down<span class="token punctuation">)</span><span class="token punctuation">;</span>
</code></pre><ul>
<li>CH455/数码管：</li>
</ul>
<pre data-role="codeBlock" data-info="c" class="language-c c"><code><span class="token keyword keyword-int">int</span> <span class="token function">ldsCh455DisplayNumber</span><span class="token punctuation">(</span><span class="token class-name">lds_ch455_handle_t</span> h<span class="token punctuation">,</span> <span class="token class-name">uint16_t</span> num<span class="token punctuation">)</span><span class="token punctuation">;</span>
</code></pre><hr>
<h2 id="内存管理">内存管理 </h2>
<ul>
<li>静态缓冲：
<ul>
<li>UART 接收缓冲（RT_SERIAL_RB_BUFSZ）在 lds_uart 线程局部；</li>
<li>各协议模块接收缓冲与帧结构：
<ul>
<li>MIC: g_rxBuffer[128], g_rxFrame；</li>
<li>DSP: g_rxBuffer[64]；</li>
<li>BASE/ThrdParty: g_rxBuffer[64]；UAC: cmdBuf[6]</li>
</ul>
</li>
<li>命令队列：MIC 16、DSP 16、BASE 8、ThirdParty 16（结构体数组，含重试计数/时间戳）</li>
</ul>
</li>
<li>动态内存：CH455 设备实例 rt_malloc；注意配对 deinit 释放与互斥删除</li>
<li>定时器/互斥/线程：均为模块级静态对象或在 init 时创建；LED/KEY/CH455 有独立线程</li>
</ul>
<hr>
<h2 id="错误处理与恢复">错误处理与恢复 </h2>
<ul>
<li>校验：UAC XOR、MIC SUM、BASE/TP CRC16（外设 CRC+互斥）、DSP 固定尾字节；均在帧尾校验失败时复位状态机</li>
<li>超时/重传：MIC/DSP/BASE/ThrdParty 均有响应超时与有限重试；DSP/MIC/BASE 清除队列项并可触发心跳或重置</li>
<li>I2C 可靠性：BK9535 指数退避、连续失败阈值触发复位+重配；CH455 写入/读取带重试与统计、健康检查</li>
<li>工测模式：lds_at 设置后，部分模块（MIC/BASE/TP）绕过业务下行以避免干扰</li>
</ul>
<hr>
<h2 id="并发与同步">并发与同步 </h2>
<ul>
<li>lds_uart：ISR → MQ → 后台线程串行分发，避免在中断中做重活</li>
<li>模块内：
<ul>
<li>互斥保护命令队列（MIC/DSP/BASE/ThrdParty）、CRC 外设（utils）、I2C 访问（CH455/BK9535）</li>
<li>定时器：
<ul>
<li>心跳（MIC/BASE/DSP/UAC）、解析超时（MIC/BASE/ThrdParty/DSP）、重传（MIC/BASE/DSP/ThrdParty）</li>
</ul>
</li>
<li>独立线程：LED、KEY 扫描、CH455 可选键盘扫描</li>
</ul>
</li>
<li>风险与规约：
<ul>
<li>回调上下文不可阻塞；尽量只做解析与入队</li>
<li>定时器回调内仅做轻量操作，避免长阻塞</li>
<li>I2C/UART 错误需记录并限次重试</li>
</ul>
</li>
</ul>

      </div>
      
      
    
    
    <script type="module">
// TODO: If ZenUML gets integrated into mermaid in the future,
//      we can remove the following lines.


var MERMAID_CONFIG = ({"startOnLoad":false});
if (typeof MERMAID_CONFIG !== 'undefined') {
  MERMAID_CONFIG.startOnLoad = false
  MERMAID_CONFIG.cloneCssStyles = false
  MERMAID_CONFIG.theme = "default"
}

mermaid.initialize(MERMAID_CONFIG || {})
if (typeof(window['Reveal']) !== 'undefined') {
  function mermaidRevealHelper(event) {
    var currentSlide = event.currentSlide
    var diagrams = currentSlide.querySelectorAll('.mermaid')
    for (var i = 0; i < diagrams.length; i++) {
      var diagram = diagrams[i]
      if (!diagram.hasAttribute('data-processed')) {
        mermaid.init(null, diagram, ()=> {
          Reveal.slide(event.indexh, event.indexv)
        })
      }
    }
  }
  Reveal.addEventListener('slidetransitionend', mermaidRevealHelper)
  Reveal.addEventListener('ready', mermaidRevealHelper)
  await mermaid.run({
    nodes: document.querySelectorAll('.mermaid')
  })
} else {
  await mermaid.run({
    nodes: document.querySelectorAll('.mermaid')
  })
}
</script>
    
    
    
  
    </body></html>