/**
 * @file lds_digital_tube.h
 * @brief 数码管显示模块头文件
 * @details 使用CH455控制器的数码管显示操作高级接口
 *          为常见数字显示任务提供简化的API，具有自动初始化
 *          和错误处理功能
 * <AUTHOR> Team
 * @version 1.0
 * @date 2025-07-15
 *
 * @copyright Copyright (c) 2025 LDS
 *
 * 功能特性:
 * - 简化的数码管显示接口
 * - 自动CH455控制器初始化
 * - 带自动格式化的数字显示
 * - 错误处理和日志记录
 * - 线程安全操作
 * - 与RT-Thread RTOS集成
 *
 * 使用示例:
 * @code
 * // 初始化数码管显示
 * if (ldsDigitalTubeInit() == 0) {
 *     // 显示一个数字
 *     ldsDigitalTubeSet(1234);
 * }
 * @endcode
 */

#ifndef __APPLICATIONS_LDS_DIGITAL_TUBE_H__
#define __APPLICATIONS_LDS_DIGITAL_TUBE_H__

#include <stdint.h>
#include <stdbool.h>
#include <rtthread.h>

#ifdef __cplusplus
extern "C" {
#endif

/**
 * @brief 初始化数码管显示模块
 * @details 初始化CH455控制器并配置为数字显示模式。
 *          在执行任何其他数码管操作之前必须调用此函数。
 *          初始化包括：
 *          - CH455 I2C通信设置
 *          - 显示模式配置
 *          - 亮度设置
 *          - 错误恢复机制
 *
 * @return int 状态码
 * @retval 0 成功 - 数码管准备就绪
 * @retval -1 初始化失败 - 检查I2C总线和CH455硬件
 *
 * @note 此函数是线程安全的，可以安全地多次调用。
 *       如果已经初始化，后续调用将返回成功。
 *
 * @warning 调用此函数前请确保I2C总线已正确配置。
 *
 * @see ldsDigitalTubeSet()
 */
int ldsDigitalTubeInit(void);
/**
 * @brief 在数码管上以十六进制显示数字
 * @details 在数码管显示器上显示16位无符号整数。
 *          数字会自动格式化并显示，前导零会被抑制。
 *          超过显示容量的数字将被截断以适应可用位数。
 *
 * @param number 要显示的数字（4位显示器为0-ffff）
 *
 * @return int 状态码
 * @retval 0 成功 - 数字正确显示
 * @retval -1 模块未初始化 - 请先调用ldsDigitalTubeInit()
 * @retval -2 无效参数 - 数字超出范围
 * @retval -3 硬件错误 - CH455通信失败
 *
 * @note 此函数是线程安全的，可以从多个线程调用。
 * @note 超过显示容量的数字将自动截断。
 * @note 前导零会自动抑制以提高可读性。
 *
 * @warning 使用此函数前请确保ldsDigitalTubeInit()调用成功。
 *
 * @see ldsDigitalTubeInit()
 *
 * @par 示例:
 * @code
 * // 显示数字0xabcf
 * int result = ldsDigitalTubeSetDigit(0xabcf);
 * if (result != 0) {
 *     rt_kprintf("Failed to display number: %d\n", result);
 * }
 * @endcode
 */
int ldsDigitalTubeSetDigit(uint16_t number);
/**
 * @brief 在数码管上显示数字
 * @details 在数码管显示器上显示16位无符号整数。
 *          数字会自动格式化并显示，前导零会被抑制。
 *          超过显示容量的数字将被截断以适应可用位数。
 *
 * @param number 要显示的数字（4位显示器为0-9999）
 *
 * @return int 状态码
 * @retval 0 成功 - 数字正确显示
 * @retval -1 模块未初始化 - 请先调用ldsDigitalTubeInit()
 * @retval -2 无效参数 - 数字超出范围
 * @retval -3 硬件错误 - CH455通信失败
 *
 * @note 此函数是线程安全的，可以从多个线程调用。
 * @note 超过显示容量的数字将自动截断。
 * @note 前导零会自动抑制以提高可读性。
 *
 * @warning 使用此函数前请确保ldsDigitalTubeInit()调用成功。
 *
 * @see ldsDigitalTubeInit()
 *
 * @par 示例:
 * @code
 * // 显示数字1234
 * int result = ldsDigitalTubeSetNumber(1234);
 * if (result != 0) {
 *     rt_kprintf("Failed to display number: %d\n", result);
 * }
 * @endcode
 */
int ldsDigitalTubeSetNumber(uint16_t number);

#ifdef __cplusplus
}
#endif

#endif /* __APPLICATIONS_LDS_DIGITAL_TUBE_H__ */
