#include <rtdevice.h>
#include "lds_utils.h"
#include "lds_uart.h"

#define DBG_TAG "UAC"
#define DBG_LVL DBG_INFO
#include <rtdbg.h>

#define UAC_SERIAL_NAME         "uart3"
#define UAC_POWER_CTRL_PIN      "PE.15"

#define CMD_HEAD_MAGIC_1 0x55
#define CMD_HEAD_MAGIC_2 0xAA
#define UAC_HB_TIMEOUT   (RT_TICK_PER_SECOND * 20)   // 20S
#define UAC_CMD_LEN      6

static rt_base_t uac_power_ctrl = -1;  /* UAC电源控制引脚 */
static rt_device_t uac_dev = RT_NULL;
static struct rt_timer heartbeatTimer;

/**
 * @brief UAC命令类型枚举
 * @details 定义UAC通信支持的命令类型
 */
typedef enum
{
    CMD_KEY_HEARTBEAT=0x00,     /**< 心跳命令 */
    CMD_KEY_UP_DOWN=0x01,       /**< 按键按下/释放命令 */
    CMD_KEY_RESP=0x02,          /**< 命令响应 */
} UAC_CMD_E;

/**
 * @brief UAC按键命令结构体
 * @details 固定6字节格式的UAC命令包结构体
 */
typedef struct
{
    uint8_t head1;              /**< 第一个魔术字节（0x55） */
    uint8_t head2;              /**< 第二个魔术字节（0xAA） */
    uint8_t len;                /**< 命令长度 */
    uint8_t cmd;                /**< 来自UAC_CMD_E的命令类型 */
    uint8_t data;               /**< 命令数据载荷 */
    uint8_t crc;                /**< 异或校验和 */
} uac_key_cmd_t;

typedef enum
{
    UAC_STATE_IDLE = 0,        /**< 初始状态 */
    UAC_STATE_HEAD1,           /**< 验证第一个魔术字节的状态 */
    UAC_STATE_HEAD2,           /**< 验证第二个魔术字节的状态 */
    UAC_STATE_LEN,             /**< 验证命令长度的状态 */
    UAC_STATE_COMMAND,         /**< 验证命令类型的状态 */
    UAC_STATE_VALUE,           /**< 验证命令数据的状态 */
} UAC_STATE_E;

static uint8_t cmdBuf[UAC_CMD_LEN];

static void ldsUacReset(void)
{
    if(uac_power_ctrl <= 0){
        LOG_E("UAC power control pin not initialized");
        return;
    }
    rt_pin_write(uac_power_ctrl, PIN_LOW);
    rt_thread_mdelay(200);
    rt_pin_write(uac_power_ctrl, PIN_HIGH);
}

/* 定时器 超时函数 */
static void ldsUacHeartbeatCheck(void* parameter)
{
    LOG_W("UAC heartbeat timeout\n");
    ldsUacReset();
}

void ldsUacKeyCmdsend(bool key_down)
{
    uac_key_cmd_t cmd = {0};
    uint8_t *data = (uint8_t *)&cmd;
    
    if(uac_dev == RT_NULL){
        LOG_E("UAC %s not initialized", UAC_SERIAL_NAME);
        return;
    }
    cmd.head1 = CMD_HEAD_MAGIC_1;
    cmd.head2 = CMD_HEAD_MAGIC_2;
    cmd.len = 0x03;
    cmd.cmd = CMD_KEY_UP_DOWN;
    cmd.data = key_down;
    cmd.crc = ldsUtilCheckXor(data, UAC_CMD_LEN - 1);
    rt_device_write(uac_dev, 0, data, sizeof(cmd));
}

/**
 * @brief 处理命令格式
 * @param data 指向命令数据的指针
 * @param size 命令数据大小
 * @return int 成功返回0，错误返回负值
 */
static int ldsUacProcessCommand(const uint8_t* data, rt_size_t size)
{
    
    uint8_t xor = 0;
    
    if (data == RT_NULL || size == 0)
    {
        LOG_E("Invalid data or size");
        return -RT_ERROR;
    }

    if(size != UAC_CMD_LEN){
        LOG_E("Invalid command length %d", size);
        LOG_HEX("uac", 8, data, size);
        return -1;
    }

    xor = ldsUtilCheckXor(data, size - 1);
    if(xor != data[size - 1]){
        LOG_E("XOR checksum error, cal %02X, get %02X", xor, data[size - 1]);
        LOG_HEX("uac", 8, data, size);
        return -1;
    }

    if(data[0] != CMD_HEAD_MAGIC_1 || data[1] != CMD_HEAD_MAGIC_2){
        LOG_E("Invalid command head %02X %02X", data[0], data[1]);
        LOG_HEX("uac", 8, data, size);
        return -1;
    }

    switch (data[3])
    {
        case CMD_KEY_HEARTBEAT:
            {
                /* 重置心跳定时器 */
                rt_timer_stop(&heartbeatTimer);
                rt_timer_start(&heartbeatTimer);
                LOG_D("Heartbeat received");
            }
            break;
        case CMD_KEY_RESP:
            {
                LOG_I("UAC key command response %d", data[4]);
            }
            break;
        default:
            LOG_W("Unknown command type: 0x%02X", data[3]);
            break;
    }
    return 0;
}

/**
 * @brief UAC数据处理函数
 *
 * @param dev RT-Thread设备句柄
 * @param data 指向接收数据缓冲区的指针
 * @param size 接收数据的字节大小
 * @return int 成功返回0，失败返回负错误码
 *
 * @warning 调用前请确保通过ldsUacInit()正确初始化
 */
int ldsUacProcess(rt_device_t dev, const uint8_t* data, rt_size_t size)
{
    static UAC_STATE_E state = UAC_STATE_IDLE;
    if (data == RT_NULL || size == 0) {
        LOG_E("Invalid data or size");
        return -RT_EINVAL;
    }

    if (dev == RT_NULL) {
        LOG_E("Invalid device handle");
        return -RT_EINVAL;
    }

    LOG_D("Received %d bytes from %s", size, dev->parent.name);

    /* 作为流数据处理 */
    for (size_t i = 0; i < size; i++) {
        switch (state) {
            case UAC_STATE_IDLE:
                if (data[i] == CMD_HEAD_MAGIC_1) {
                    cmdBuf[0] = data[i];
                    state = UAC_STATE_HEAD1;
                } else {
                    LOG_E("Invalid head1: 0x%02X", data[i]);
                    state = UAC_STATE_IDLE;
                }
                break;
            case UAC_STATE_HEAD1:
                if (data[i] == CMD_HEAD_MAGIC_2) {
                    state = UAC_STATE_HEAD2;
                    cmdBuf[1] = data[i];
                } else {
                    LOG_E("Invalid head2: 0x%02X", data[i]);
                    state = UAC_STATE_IDLE;
                }
                break;
            case UAC_STATE_HEAD2:
                if (data[i] == 0x03) {
                    state = UAC_STATE_LEN;
                    cmdBuf[2] = data[i];
                } else {
                    LOG_E("Invalid len: 0x%02X", data[i]);
                    state = UAC_STATE_IDLE;
                }
                break;
            case UAC_STATE_LEN:
                cmdBuf[3] = data[i];
                state = UAC_STATE_COMMAND;
                break;
            case UAC_STATE_COMMAND:
                state = UAC_STATE_VALUE;
                cmdBuf[4] = data[i];
                break;
            case UAC_STATE_VALUE:
                cmdBuf[5] = data[i];
                if(ldsUacProcessCommand(cmdBuf, sizeof(cmdBuf))){
                    LOG_HEX("uac-o", 8, data, size);
                }
                state = UAC_STATE_IDLE;
                rt_memset(cmdBuf, 0, sizeof(cmdBuf));
                break;
        }
    }

    return 0;
}

/**
 * @brief 初始化支持流处理的UAC设备
 * @details 初始化UAC硬件、UART通信、流缓冲区
 *          和后台处理线程
 *
 * @return int 成功返回0，失败返回负错误码
 *
 * @note 此函数执行完整的UAC系统初始化，包括：
 *       - 电源控制引脚设置
 *       - 带DMA的UART接口初始化
 *       - 流缓冲区分配和设置
 *       - 后台处理线程创建
 *       - 心跳定时器配置
 */
int ldsUacInit(void)
{
    rt_memset(cmdBuf, 0, sizeof(cmdBuf));

    /* 初始化电源控制 */
    uac_power_ctrl = power_ctrl_pin_init(UAC_POWER_CTRL_PIN, PIN_HIGH);
    if (uac_power_ctrl < 0) {
        LOG_E("Failed to initialize UAC power control pin %s", UAC_POWER_CTRL_PIN);
        return -RT_ERROR;
    }

    /* 复位UAC设备 */
    ldsUacReset();

    /* 使用回调初始化UART */
    uac_dev = ldsUartInit(UAC_SERIAL_NAME, LDS_UART_INDEX_3, ldsUacProcess);
    if(uac_dev == RT_NULL) {
        LOG_E("Failed to initialize UAC UART %s", UAC_SERIAL_NAME);
        return -RT_ERROR;
    }

    /* 初始化心跳定时器 */
    rt_timer_init(&heartbeatTimer, "UAChb",
                  ldsUacHeartbeatCheck,
                  RT_NULL,
                  UAC_HB_TIMEOUT,
                  RT_TIMER_FLAG_SOFT_TIMER | RT_TIMER_FLAG_PERIODIC);
    rt_timer_start(&heartbeatTimer);

    return 0;
}
