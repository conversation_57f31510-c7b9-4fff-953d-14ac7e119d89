#ifndef __APPLICATIONS_LDS_UTILS_H__
#define __APPLICATIONS_LDS_UTILS_H__
/**
 * @brief 初始化电源控制引脚
 *
 * @param pin 引脚名称
 * @param value 要设置的初始值
 * @return rt_base_t
 */
rt_base_t power_ctrl_pin_init(const char *pin, rt_uint8_t value);
/**
 * @brief 计算数据缓冲区的异或校验和
 *
 * @param data 数据缓冲区
 * @param len 数据缓冲区的长度
 * @return uint8_t 异或校验和
 */
uint8_t ldsUtilCheckXor(const uint8_t *data, size_t len);
/**
 * @brief 计算数据缓冲区的求和校验和
 *
 * @param data 数据缓冲区
 * @param len 数据缓冲区的长度
 * @return uint8_t 求和校验和
 */
uint8_t ldsUtilCheckSum(const uint8_t *data, size_t len);
/**
 * @brief 反转32位整数中指定字节数的位顺序
 *
 * @param dword 输入的32位整数。使用低位进行反转。
 * @param len 要考虑进行位反转的字节数（从最低有效端开始）。
 *            例如，如果len为1，则反转8位。如果为2，则反转16位。
 *            len的有效范围为1到4。
 * @return 具有指定位数反转的整数。出错时返回0。
 */
uint32_t ldsUtilReverseBits(uint32_t dword, size_t len);
/**
 * @brief 计算数据缓冲区的CRC16-modbus校验和
 *
 * @param data 数据缓冲区
 * @param len 数据缓冲区的长度
 * @return uint16_t CRC16校验和
 */
uint16_t ldsUtilCheckCrc16(const uint8_t *data, size_t len);

/**
 * @brief 计算数据缓冲区的CRC32校验和
 *
 * @param data 数据缓冲区
 * @param len 数据缓冲区的长度
 * @return uint32_t CRC32校验和
 */
uint32_t ldsUtilCheckCrc32(const uint8_t *data, uint32_t len);

#endif // !__APPLICATIONS_LDS_UTILS_H__