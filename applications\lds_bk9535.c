/**
 * @file lds_bk9535.c
 * <AUTHOR> name (<EMAIL>)
 * @brief This file contains the implementation for the BK9535 and BK9529 chip
 * driver.
 * @version 0.1
 * @date 2025-05-19
 *
 * @copyright Copyright (c) 2025
 *
 */

#include "lds_bk9535.h"

#include <rtdevice.h>
#include <rtthread.h>
#include "n32g45x.h"

#define DBG_TAG "BK9535"
#include <rtdbg.h>

#define CHIP_DEV_BK9535 0x25
#define BK9535_WAIT_TIME 3

#define BK9535_CE_PIN "PA.15"
#define BK9535_SDA_PIN "PB.7"
#define BK9535_SCL_PIN "PB.6"
#define BK9535_I2C_DEV_NAME "i2c0"
#define BK9535_I2C_ADDR CHIP_DEV_BK9535

#define REG_VAL_CNT         4
#define BK9535_REG_VAL_NUM  36
#define BK9535_REG_FREQ_NUM 64
#define BK9535_TX_9DBM      0x79
#define BK9535_TX_13DBM     0xAF
/* I2C Reliability Configuration */
#define LDS_BK9535_I2C_RETRY_COUNT_DEFAULT      3
#define LDS_BK9535_I2C_RETRY_DELAY_BASE_MS      2
#define LDS_BK9535_I2C_RETRY_DELAY_MAX_MS       50
#define LDS_BK9535_REINIT_FAILURE_THRESHOLD     5
#define LDS_BK9535_I2C_TIMEOUT_MS               100

static void lds_bk9535_set_regvalue_default(void);
static void lds_bk9535_reset_chip(void);

static struct rt_i2c_bus_device* i2c_dev = RT_NULL;
static rt_base_t ce_pin = -1;
static rt_base_t sda_pin = -1;
static rt_base_t scl_pin = -1;

static int16_t gFreqIndex = -1;
static int16_t gPowerValue = BK9535_TX_9DBM;

/* I2C reliability enhancement variables */
static uint8_t g_bk9535I2c_consecutive_failures = 0;
static lds_bk9535_i2c_config_t g_bk9535I2cConfig = {
    .retry_count = LDS_BK9535_I2C_RETRY_COUNT_DEFAULT,
    .retry_delay_base_ms = LDS_BK9535_I2C_RETRY_DELAY_BASE_MS,
    .retry_delay_max_ms = LDS_BK9535_I2C_RETRY_DELAY_MAX_MS,
    .reinit_threshold = LDS_BK9535_REINIT_FAILURE_THRESHOLD,
    .timeout_ms = LDS_BK9535_I2C_TIMEOUT_MS
};

static const uint8_t bk9535_reg_val[BK9535_REG_VAL_NUM][REG_VAL_CNT] = {
    { 0x1E, 0x44, 0x0C, 0x88 }, // REG00
    { 0x04, 0xFF, 0x00, 0x56 }, // REG01
    { 0x89, 0x5C, 0x02, 0x9B }, // REG02
    { 0xEF, 0x24, 0x04, 0x00 }, // REG03
    { 0x5D, 0x88, 0x00, 0x44 }, // REG04
    { 0x00, 0x28, 0x03, 0x80 }, // REG05
    { 0x59, 0x00, 0x78, 0x80 }, // REG06
    { 0x1E, 0x40, 0x00, 0x00 }, // REG07
    { 0x00, 0x08, 0x01, 0x00 }, // REG08,0xAA, 0x08, 0x01, 0x00-->0x00, 0x08, 0x01, 0x00,
    { 0x00, 0x00, 0x81, 0x03 }, // REG09
    { 0x32, 0xAF, 0x21, 0x1D }, // REG0A,0x72, 0xAB, 0x21, 0x1D-->0x32, 0xAF, 0x21, 0x1D,
    { 0x00, 0x00, 0xC0, 0x05 }, // REG0B
    { 0x00, 0x00, 0x02, 0xDE }, // REG0C
    { 0x3A, 0x98, 0x00, 0x00 }, // REG0D
    { 0x28, 0x28, 0x28, 0x28 }, // REG30
    { 0xD1, 0x00, 0x00, 0x28 }, // REG31
    { 0x10, 0x06, 0x00, 0x64 }, // REG32
    { 0x58, 0x02, 0x6C, 0x02 }, // REG33,0x48, 0x80, 0x8D, 0x82-->0x58, 0x02, 0x6C, 0x02,
    { 0x0B, 0x1B, 0x32, 0x1B }, // REG34,0x0B, 0x02, 0x11, 0x08-->0x0B, 0x1B, 0x32, 0x1B,
    { 0x70, 0x50, 0x00, 0x80 }, // REG35
    { 0x0F, 0x80, 0x1E, 0x04 }, // REG36
    { 0x00, 0x00, 0x00, 0x00 }, // REG37
    { 0x00, 0x00, 0x00, 0x00 }, // REG38,0x00, 0x00, 0x00, 0x00
    { 0x03, 0xD7, 0xD5, 0xF7 }, // REG39
    { 0xC0, 0x25, 0x00, 0xFF }, // REG3A,0xC0, 0x25, 0x00, 0x74, -->0xC0, 0x25, 0x00, // 0xFF.用户的数据改为0xFF,判断厂测
    { 0x85, 0x25, 0x00, 0x3A }, // REG3B
    { 0x77, 0x01, 0x00, 0x3B }, // REG3C
    { 0x95, 0x23, 0x00, 0x3C }, // REG3D
    { 0x00, 0xF8, 0x07, 0xC0 }, // REG3E
    { 0x80, 0x0F, 0x00, 0x00 }, // REG3F
    { 0x00, 0x00, 0x95, 0x35 }, // REG70
    { 0x21, 0x82, 0x08, 0x10 }, // REG71
    { 0x00, 0x5A, 0x12, 0x1A }, // REG72
    { 0x00, 0x00, 0x00, 0x00 }, // REG73
    { 0x00, 0x4A, 0x30, 0x53 }, // REG77
    { 0x00, 0x00, 0x00, 0x08 }, // REG78
};

// 发送频点和寄存器0D之间的公式对应关系,f的单位是MHz
// Reg_0x0D = f*6*2^23/24.576
static const uint8_t bk9535_reg_freq[BK9535_REG_FREQ_NUM][REG_VAL_CNT] = {
    { 0x4E, 0xF1, 0x60, 0x00 }, // 646.7
    { 0x4F, 0x07, 0x40, 0x00 }, // 647.4
    { 0x4F, 0x1D, 0x20, 0x00 }, // 648.1
    { 0x4F, 0x33, 0x00, 0x00 }, // 648.8
    { 0x4F, 0x48, 0xE0, 0x00 }, // 649.5
    { 0x4F, 0x5E, 0xC0, 0x00 }, // 650.2
    { 0x4F, 0x74, 0xA0, 0x00 }, // 650.9
    { 0x4F, 0x8A, 0x80, 0x00 }, // 651.6
    { 0x4F, 0xA0, 0x60, 0x00 }, // 652.3
    { 0x4F, 0xB6, 0x40, 0x00 }, // 653
    { 0x4F, 0xCC, 0x20, 0x00 }, // 653.7
    { 0x4F, 0xE2, 0x00, 0x00 }, // 654.4
    { 0x4F, 0xF7, 0xE0, 0x00 }, // 655.1
    { 0x50, 0x0D, 0xC0, 0x00 }, // 655.8
    { 0x50, 0x23, 0xA0, 0x00 }, // 656.5
    { 0x50, 0x39, 0x80, 0x00 }, // 657.2
    { 0x50, 0x4F, 0x60, 0x00 }, // 657.9
    { 0x50, 0x65, 0x40, 0x00 }, // 658.6
    { 0x50, 0x7B, 0x20, 0x00 }, // 659.3
    { 0x50, 0x91, 0x00, 0x00 }, // 660
    { 0x50, 0xA6, 0xE0, 0x00 }, // 660.7
    { 0x50, 0xBC, 0xC0, 0x00 }, // 661.4
    { 0x50, 0xD2, 0xA0, 0x00 }, // 662.1
    { 0x50, 0xE8, 0x80, 0x00 }, // 662.8
    { 0x51, 0x14, 0x40, 0x00 }, // 664.2//
    { 0x51, 0x2A, 0x20, 0x00 }, // 664.9
    { 0x51, 0x40, 0x00, 0x00 }, // 665.6
    { 0x51, 0x55, 0xE0, 0x00 }, // 666.3
    { 0x51, 0x6B, 0xC0, 0x00 }, // 667
    { 0x51, 0x81, 0xA0, 0x00 }, // 667.7
    { 0x51, 0x97, 0x80, 0x00 }, // 668.4
    { 0x51, 0xAD, 0x60, 0x00 }, // 669.1
    { 0x51, 0xC3, 0x40, 0x00 }, // 669.8
    { 0x51, 0xD9, 0x20, 0x00 }, // 670.5
    { 0x51, 0xEF, 0x00, 0x00 }, // 671.2
    { 0x52, 0x04, 0xE0, 0x00 }, // 671.9
    { 0x52, 0x1A, 0xC0, 0x00 }, // 672.6
    { 0x52, 0x30, 0xA0, 0x00 }, // 673.3
    { 0x52, 0x46, 0x80, 0x00 }, // 674
    { 0x52, 0x5C, 0x60, 0x00 }, // 674.7
    { 0x52, 0x72, 0x40, 0x00 }, // 675.4
    { 0x52, 0x88, 0x20, 0x00 }, // 676.1
    { 0x52, 0x9E, 0x00, 0x00 }, // 676.8
    { 0x52, 0xB3, 0xE0, 0x00 }, // 677.5
    { 0x52, 0xC9, 0xC0, 0x00 }, // 678.2
    { 0x52, 0xF5, 0x80, 0x00 }, // 679.6//
    { 0x53, 0x0B, 0x60, 0x00 }, // 680.3
    { 0x53, 0x21, 0x40, 0x00 }, // 681
    { 0x53, 0x37, 0x20, 0x00 }, // 681.7
    { 0x53, 0x4D, 0x00, 0x00 }, // 682.4
    { 0x53, 0x62, 0xE0, 0x00 }, // 683.1
    { 0x53, 0x78, 0xC0, 0x00 }, // 683.8
    { 0x53, 0x8E, 0xA0, 0x00 }, // 684.5
    { 0x53, 0xBA, 0x60, 0x00 }, // 685.9//
    { 0x53, 0xD0, 0x40, 0x00 }, // 686.6
    { 0x53, 0xE6, 0x20, 0x00 }, // 687.3
    { 0x54, 0x11, 0xE0, 0x00 }, // 688.7//
    { 0x54, 0x27, 0xC0, 0x00 }, // 689.4
    { 0x54, 0x69, 0x60, 0x00 }, // 691.5//
    { 0x54, 0x7F, 0x40, 0x00 }, // 692.2
    { 0x54, 0xAB, 0x00, 0x00 }, // 693.6//
    { 0x54, 0xD6, 0xC0, 0x00 }, // 695//
    { 0x54, 0xEC, 0xA0, 0x00 }, // 695.7
    { 0x55, 0x18, 0x60, 0x00 }, // 697.1//
};

void ldsBk9535SetCePin(bool high)
{
    rt_pin_write(ce_pin, high ? PIN_HIGH : PIN_LOW);
}
/**
 * @brief Calculate retry delay with exponential backoff
 *
 * @param retry_count Current retry attempt count
 * @return uint32_t Delay in milliseconds
 */
static uint32_t ldsBk9535CalculateRetryDelay(uint8_t retry_count)
{
    uint32_t delay = g_bk9535I2cConfig.retry_delay_base_ms;

    // Exponential backoff: delay = base * 2^retry_count
    for (uint8_t i = 0; i < retry_count && delay < g_bk9535I2cConfig.retry_delay_max_ms; i++) {
        delay *= 2;
        if (delay > g_bk9535I2cConfig.retry_delay_max_ms) {
            delay = g_bk9535I2cConfig.retry_delay_max_ms;
            break;
        }
    }

    return delay;
}

/**
 * @brief Update I2C operation statistics
 *
 * @param operation_success True if operation was successful, false otherwise
 */
static void ldsBk9535UpdateI2cStats(bool operation_success)
{
    if (operation_success) {
        g_bk9535I2c_consecutive_failures = 0;
    } else {
        g_bk9535I2c_consecutive_failures++;
    }
}

/**
 * @brief Check if BK9535 re-initialization should be performed
 *
 * @return true if re-initialization is needed, false otherwise
 */
static bool ldsBk9535ShouldReinitialize(void)
{
    return (g_bk9535I2c_consecutive_failures >= g_bk9535I2cConfig.reinit_threshold);
}

/**
 * @brief Perform BK9535 re-initialization after consecutive failures
 */
static void ldsBk9535PerformReinitialize(void)
{
    LOG_W("Performing BK9535 re-initialization due to consecutive I2C failures");

    // Reset statistics
    g_bk9535I2c_consecutive_failures = 0;

    // Perform hardware reset sequence
    if (ce_pin >= 0) {
        rt_pin_write(ce_pin, PIN_LOW);
        rt_thread_mdelay(10);
        rt_pin_write(ce_pin, PIN_HIGH);
        rt_thread_mdelay(100);
    }

    // Re-initialize default register values
    lds_bk9535_set_regvalue_default();
    rt_thread_mdelay(10);

    // Reset chip state machine
    lds_bk9535_reset_chip();
    rt_thread_mdelay(10);
    if(gFreqIndex < 0) {
        LOG_E("BK9535 freq index not set");
        return;
    }
    if(lds_bk9535_set_reg0D_lookfor_table(gFreqIndex) == 0) {
        rt_thread_mdelay(1);
        lds_bk9535_set_reg_freq_after_init();
        LOG_I("BK9535 re-initialization completed");
    } else {
        LOG_E("BK9535 re-initialization failed");
    }
}

//读BK9535寄存器
uint8_t lds_bk9535_ReadData(uint8_t* dat,uint8_t cnt,uint8_t regAddr)
{
    rt_ssize_t ret = 0;
    uint8_t retry_count = 0;
    bool operation_success = false;

    if (dat == RT_NULL || cnt == 0) {
        LOG_E("Invalid parameters for read operation");
        return -1;
    }

    for (retry_count = 0; retry_count <= g_bk9535I2cConfig.retry_count; retry_count++) {
        uint8_t addr = regAddr << 1 | 1; // 读数据时，地址需要左移一位并加1

        // Send register address
        ret = rt_i2c_master_send(i2c_dev, BK9535_I2C_ADDR,
                                RT_I2C_NO_STOP | RT_I2C_DEV_ID,
                                &addr, 1);
        if (ret < 0) {
            if (retry_count < g_bk9535I2cConfig.retry_count) {
                LOG_W("I2C send addr failed! ret = %d, retry %d/%d",
                      ret, retry_count + 1, g_bk9535I2cConfig.retry_count);
                rt_thread_mdelay(ldsBk9535CalculateRetryDelay(retry_count));
                continue;
            } else {
                LOG_E("I2C send addr failed after %d retries! ret = %d",
                      g_bk9535I2cConfig.retry_count, ret);
                break;
            }
        }

        // Receive data
        ret = rt_i2c_master_recv(i2c_dev, regAddr, RT_I2C_NO_START, dat, cnt);
        if (ret < 0) {
            if (retry_count < g_bk9535I2cConfig.retry_count) {
                LOG_W("I2C recv reg 0x%02x failed! ret = %d, retry %d/%d",
                      regAddr, ret, retry_count + 1, g_bk9535I2cConfig.retry_count);
                rt_thread_mdelay(ldsBk9535CalculateRetryDelay(retry_count));
                continue;
            } else {
                LOG_E("I2C recv reg 0x%02x failed after %d retries! ret = %d",
                      regAddr, g_bk9535I2cConfig.retry_count, ret);
                break;
            }
        }

        // Operation successful
        operation_success = true;
        break;
    }

    // Update statistics
    ldsBk9535UpdateI2cStats(operation_success);

    // Check if re-initialization is needed due to consecutive failures
    if (!operation_success && ldsBk9535ShouldReinitialize()) {
        LOG_W("Triggering BK9535 re-initialization due to hardware I2C read failures");
        ldsBk9535PerformReinitialize();
    }

    return operation_success ? 0 : -1;
}

//写BK9535寄存器
uint8_t lds_bk9535_WriteData(const uint8_t* dat,uint8_t cnt,uint8_t regAddr)
{
    rt_ssize_t ret = 0;
    uint8_t retry_count = 0;
    bool operation_success = false;

    if (dat == RT_NULL || cnt == 0) {
        LOG_E("Invalid parameters for write operation");
        return -1;
    }

    for (retry_count = 0; retry_count <= g_bk9535I2cConfig.retry_count; retry_count++) {
        uint8_t addr = regAddr << 1;

        // Send register address
        ret = rt_i2c_master_send(i2c_dev, BK9535_I2C_ADDR,
                                RT_I2C_NO_STOP | RT_I2C_DEV_ID,
                                &addr, 1);
        if (ret < 0) {
            if (retry_count < g_bk9535I2cConfig.retry_count) {
                LOG_W("I2C send addr failed! ret = %d, retry %d/%d",
                      ret, retry_count + 1, g_bk9535I2cConfig.retry_count);
                rt_thread_mdelay(ldsBk9535CalculateRetryDelay(retry_count));
                continue;
            } else {
                LOG_E("I2C send addr failed after %d retries! ret = %d",
                      g_bk9535I2cConfig.retry_count, ret);
                break;
            }
        }

        // Send data
        ret = rt_i2c_master_send(i2c_dev, addr, RT_I2C_NO_START, dat, cnt);
        if (ret < 0) {
            if (retry_count < g_bk9535I2cConfig.retry_count) {
                LOG_W("I2C send reg 0x%02x data failed! ret = %d, retry %d/%d",
                      regAddr, ret, retry_count + 1, g_bk9535I2cConfig.retry_count);
                rt_thread_mdelay(ldsBk9535CalculateRetryDelay(retry_count));
                continue;
            } else {
                LOG_E("I2C send reg 0x%02x data failed after %d retries! ret = %d",
                      regAddr, g_bk9535I2cConfig.retry_count, ret);
                break;
            }
        }

        // Operation successful
        operation_success = true;
        break;
    }

    // Update statistics
    ldsBk9535UpdateI2cStats(operation_success);

    // Check if re-initialization is needed due to consecutive failures
    if (!operation_success && ldsBk9535ShouldReinitialize()) {
        LOG_W("Triggering BK9535 re-initialization due to hardware I2C write failures");
        ldsBk9535PerformReinitialize();
    }

    return operation_success ? 0 : -1;
}
// init
static int lds_bk9535_hardware_init(void)
{
    sda_pin = rt_pin_get(BK9535_SDA_PIN);
    if (sda_pin < 0) {
        LOG_E("SDA pin %s not found!", BK9535_SDA_PIN);
        return -1;
    }
    rt_pin_mode(sda_pin, PIN_MODE_OUTPUT_OD);
    scl_pin = rt_pin_get(BK9535_SCL_PIN);
    if (scl_pin < 0) {
        LOG_E("SCL pin %s not found!", BK9535_SCL_PIN);
        return -1;
    }
    rt_pin_mode(scl_pin, PIN_MODE_OUTPUT_OD);
    if(i2c_dev == RT_NULL){
        i2c_dev = (struct rt_i2c_bus_device*)rt_device_find(BK9535_I2C_DEV_NAME);
        if (i2c_dev == RT_NULL) {
            LOG_E("I2C device not found!");
            return -1;
        }    
    }
    if (ce_pin < 0) {
        ce_pin = rt_pin_get(BK9535_CE_PIN);
        if (ce_pin < 0) {
            LOG_E("CE pin %s not found!", BK9535_CE_PIN);
            return -1;
        }
    }
    rt_pin_mode(ce_pin, PIN_MODE_OUTPUT);
    rt_pin_write(ce_pin, PIN_LOW); // Set CE pin high
    rt_thread_mdelay(10);
    rt_pin_write(ce_pin, PIN_HIGH); // Set CE pin high
    rt_thread_mdelay(100);
    uint32_t data = 0;
    rt_err_t ret = lds_bk9535_ReadData((uint8_t*)&data, sizeof(data), 0x70);
    if (ret < 0) {
        LOG_E("I2C recv failed! ret = %d", ret);
        return -1;
    }
    LOG_I("I2C recv success! CHIP ID = 0x%08x", data);
    return 0;
}
static void lds_bk9535_set_regvalue_default(void)
{
    uint8_t u8addr = 0;
    uint8_t i = 0;

    for (i = 0; i < BK9535_REG_VAL_NUM; i++) {
        if (i <= 13) // REG0x0~0x0D--0-13
            u8addr = i;
        else if (i <= 29) // REG0x30~0x3F--14-29
            u8addr = (0x30 - 14) + i;
        else if (i <= 33) // REG0x70~0x73--30-33
            u8addr = (0x70 - 30) + i;
        else if (i <= 35) // REG0x77~0x78--34-35
            u8addr = (0x77 - 34) + i;

        lds_bk9535_WriteData(bk9535_reg_val[i], REG_VAL_CNT, u8addr); // rx_reg_val=rx_48k_uband values
    }
}

// Reset chip state machine
static void lds_bk9535_reset_chip(void)
{
    uint8_t u8reg_val[4] = { 0x00 };

    lds_bk9535_ReadData(u8reg_val, 4, 0x3F);
    u8reg_val[1] &= ~0x08; //
    lds_bk9535_WriteData(u8reg_val, 4, 0x3F);
    u8reg_val[1] |= 0x08; //
    lds_bk9535_WriteData(u8reg_val, 4, 0x3F);
}

/**
 * @brief Sets the TX power by configuring register 0A of the BK9535 device.
 *
 * @param power_high True for high tx power and false for low tx power.
 * @return int Returns 0 on success, or a negative error code on failure.
 */
int lds_bk9535_set_reg0A_tx_power(bool power_high)
{
    uint8_t u8Reg0a_buf[4] = { 0x32, 0xAF, 0x21, 0x1D };
    uint8_t rx_buf[4] = { 0x00 };

    lds_bk9535_ReadData(u8Reg0a_buf, 4, 0x0A); // 读0x0x0A
    LOG_D("Read REG0A: %02X %02X %02X %02X", u8Reg0a_buf[0], u8Reg0a_buf[1], u8Reg0a_buf[2], u8Reg0a_buf[3]);
    if(power_high){
        u8Reg0a_buf[1] = BK9535_TX_13DBM;     
    } else {
        u8Reg0a_buf[1] = BK9535_TX_9DBM;
    }
    gPowerValue = u8Reg0a_buf[1];
    lds_bk9535_WriteData(u8Reg0a_buf, 4, 0x0A); // 设定0x0A

    lds_bk9535_ReadData(rx_buf,4,0x0A);
    if(rt_memcmp(rx_buf, u8Reg0a_buf,4) == 0)
    {
        LOG_I("Set power high %d success ", power_high);
        return 0;
    }
    else
    {
        LOG_E("Set power high %d Fail set %08X, get %08X", power_high, *((uint32_t *)u8Reg0a_buf), *((uint32_t *)rx_buf));
        return -1;
    }
}

/**
 * @brief Sets the REG0D value using a lookup table based on the provided index.
 *
 * This function selects a value from a predefined lookup table and sets the REG0D register
 * accordingly. The lookup table is indexed by the parameter u8TableIndex.
 *
 * @param u8TableIndex The index into the lookup table to select the desired REG0D value.
 * @return int Returns 0 on success, or a negative error code on failure (e.g., if the index is out of range).
 */
int lds_bk9535_set_reg0D_lookfor_table(uint8_t u8TableIndex)
{
    uint8_t u8RegBuf[4] = { 0 };
    uint8_t u8RegBufRead[4] = {0};

    // Bounds checking for table index
    if (u8TableIndex >= BK9535_REG_FREQ_NUM) {
        LOG_E("Invalid frequency table index: %d (max: %d)", u8TableIndex, BK9535_REG_FREQ_NUM - 1);
        return -1;
    }

    u8RegBuf[0] = bk9535_reg_freq[u8TableIndex][0];
    u8RegBuf[1] = bk9535_reg_freq[u8TableIndex][1];
    u8RegBuf[2] = bk9535_reg_freq[u8TableIndex][2];
    u8RegBuf[3] = bk9535_reg_freq[u8TableIndex][3];
    lds_bk9535_WriteData(u8RegBuf, 4, 0x0D);

    //读取0x0D寄存器的值，并与设置值比较
    lds_bk9535_ReadData(u8RegBufRead,4,0x0D);
    if(rt_memcmp(u8RegBufRead, u8RegBuf,4) == 0)
    {
        gFreqIndex = u8TableIndex;
        LOG_I("Set index %d Freq Success %08X ", u8TableIndex, *((uint32_t *)u8RegBuf));
        return 0;
    }
    else
    {
        LOG_E("Set index %d Freq Fail set %08X, get %08X", u8TableIndex, *((uint32_t *)u8RegBuf), *((uint32_t *)u8RegBufRead));
        return -1;
    }
}
void lds_bk9535_prevent_rf_unlock(void)
{
    uint8_t rx_buf[4] = { 0x00 };
    if(gFreqIndex == -1) {
        return;
    }
    lds_bk9535_ReadData(rx_buf, 4, 0x72); // 读0x72-6
    // LOG_HEX("bk9529", 4, rx_buf, 4);
    if((rx_buf[2]&0x01) == 0) {
        return;    
    }
    LOG_W("RF unlock detected, resetting frequency");
    //检测到失锁需要重新设频点
    if(lds_bk9535_set_reg0D_lookfor_table(gFreqIndex) == 0) {
        rt_thread_mdelay(1);
        lds_bk9535_set_reg_freq_after_init();
    }
}
// 设置频点后初始化
void lds_bk9535_set_reg_freq_after_init(void)
{
    uint8_t rx_buf[4] = { 0x00 };
    // uint8_t u8Reg0d_buf[4] = { 0x4E, 0xF1, 0x60, 0x00 };
    uint8_t u8Reg38_buf[4] = {
        0x00, 0x00, 0x09, 0x01
    }; // 用户ID {0x00,0x00,0x09,0x01}-->{0xFF,0x00,0x09,0x01}

    uint8_t u8Reg3f_buf0[4] = { 0x80, 0x07, 0x00, 0x00 };
    uint8_t u8Reg3f_buf1[4] = { 0x80, 0x0F, 0x00, 0x00 };

    uint8_t u8Reg03_buf0[4] = { 0xEF, 0xA4, 0x84, 0x00 };
    uint8_t u8Reg03_buf1[4] = { 0xEF, 0x24, 0x84, 0x00 };

    uint8_t u8Reg0a_buf[4] = { 0x32, 0xAF, 0x21, 0x1D };
    
    u8Reg0a_buf[1] = gPowerValue;

    lds_bk9535_WriteData(u8Reg38_buf, 4, 0x38); // 写0x38-用户ID-3
    lds_bk9535_ReadData(rx_buf, 4, 0x3F); // 读0x3F-4
    lds_bk9535_WriteData(u8Reg3f_buf0, 4, 0x3F); // 写0x3F-4
    rt_thread_mdelay(1);
    lds_bk9535_WriteData(u8Reg3f_buf1, 4, 0x3F); // 写0x3F-4
    rt_thread_mdelay(5);
    lds_bk9535_WriteData(u8Reg03_buf0, 4, 0x03); // 设定0x03两次-5
    lds_bk9535_WriteData(u8Reg03_buf1, 4, 0x03);
    rt_thread_mdelay(1);
    lds_bk9535_ReadData(rx_buf, 4, 0x72); // 读0x72-6
    lds_bk9535_WriteData(u8Reg0a_buf, 4, 0x0A); // 设定0x0A-7
}

/**
 * @brief Initializes the BK9535/BK9529 device with the specified frequency index.
 *
 * This function sets up the BK9535 or BK9529 audio device using the provided
 * frequency index. It configures the necessary hardware registers and prepares
 * the device for operation.
 *
 * @return int Returns 0 on success, or a negative error code on failure.
 */
int lds_bk9535_bk9529_init(void)
{
    if (lds_bk9535_hardware_init()) {
        return -1;
    }
    lds_bk9535_set_regvalue_default(); // 写I2C默认寄存器配置
    rt_thread_mdelay(10);
    lds_bk9535_reset_chip();
    rt_thread_mdelay(10); // 给BK9535写初始化寄存器的时间
    return 0;
}
/*
// Test function, change freq
void bk9535_test(int argc, char* argv[])
{
    uint8_t freq_index = 0;
    if (argc == 2) {
        freq_index = atoi(argv[1]);
    } else {
        freq_index = 0;
    }
    LOG_I("BK9535 test, freq_index = %d", freq_index);
    lds_bk9535_bk9529_init(freq_index);
}
MSH_CMD_EXPORT(bk9535_test, bk9535_test);
*/