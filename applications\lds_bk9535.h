#ifndef _LDS_BK9535_H_
#define _LDS_BK9535_H_

#include <stdint.h>
#include <stdbool.h>
#ifdef __cplusplus
extern "C" {
#endif

/**
 * @brief 设置BK9535/BK9529设备的CE引脚
 *
 * 此函数设置BK9535或BK9529音频设备的CE（芯片使能）引脚。
 * CE引脚用于启用或禁用设备。
 *
 * @param high true设置CE引脚为高电平（启用设备），false设置为低电平（禁用设备）
 */
void ldsBk9535SetCePin(bool high);
/**
 * @brief 通过配置BK9535设备的寄存器0A来设置发射功率
 *
 * @param power_high true表示高发射功率，false表示低发射功率
 * @return int 成功返回0，失败返回负错误码
 */
int lds_bk9535_set_reg0A_tx_power(bool power_high);
/**
 * @brief 使用基于提供索引的查找表设置REG0D值
 *
 * 此函数从预定义的查找表中选择一个值并相应地设置REG0D寄存器。
 * 查找表由参数u8TableIndex索引。
 *
 * @param u8TableIndex 查找表的索引，用于选择所需的REG0D值
 * @return int 成功返回0，失败返回负错误码（例如，如果索引超出范围）
 */
int lds_bk9535_set_reg0D_lookfor_table(uint8_t u8TableIndex);
/**
 * @brief 防止射频解锁
 *
 */
void lds_bk9535_prevent_rf_unlock(void);
/**
 * @brief 设置频率后设置寄存器值
 *
 */
void lds_bk9535_set_reg_freq_after_init(void);

/* I2C配置结构体 */
typedef struct {
    uint8_t retry_count;            /**< 重试次数 */
    uint8_t retry_delay_base_ms;    /**< 重试间的基础延时 */
    uint8_t retry_delay_max_ms;     /**< 重试间的最大延时 */
    uint8_t reinit_threshold;       /**< 重新初始化的失败阈值 */
    uint16_t timeout_ms;            /**< I2C操作超时时间 */
} lds_bk9535_i2c_config_t;

/**
 * @brief 使用指定的频率索引初始化BK9535/BK9529设备
 *
 * 此函数使用提供的频率索引设置BK9535或BK9529音频设备。
 * 它配置必要的硬件寄存器并为设备操作做准备。
 *
 * @return int 成功返回0，失败返回负错误码
 */
int lds_bk9535_bk9529_init(void);
#ifdef __cplusplus
}
#endif

#endif
