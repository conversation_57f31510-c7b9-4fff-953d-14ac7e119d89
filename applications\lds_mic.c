/**
 * @file lds_mic.c
 * @brief LDS麦克风通信协议栈实现
 * @details 此文件实现了麦克风设备的完整通信协议栈
 *          遵循指定的大端字节序协议格式。
 *          此版本包含命令队列，用于强健的多命令处理。
 * <AUTHOR> Team
 * @version 2.0
 * @date 2025-07-21
 *
 * @copyright Copyright (c) 2025 LDS
 *
 * 协议格式:
 * Head(1) + modelId(2) + CMD(2) + SEQ(1) + Addr(1) + LEN(2) + DATA(可变) + SUM(1)
 * - 大端字节序（MSB在前）
 * - Head: 所有帧固定为0x5A
 * - modelId: 0x0001-0xFFFFF设备唯一标识符
 * - SEQ: 0x00-0xFF序列号，用于请求/响应匹配
 * - Addr: 主机=0x00, 从机=0x01/0x02/0x03, 从机广播=0xF1, 未使用=0xEE, 全局广播=0xFF
 * - CMD: 定义操作的功能码
 * - LEN: DATA字段的字节长度
 * - DATA: 可变长度载荷数据
 * - SUM: 校验和（从Head到SUM-1所有字节的和，取低8位）
 */

#include <rtdevice.h>
#include <stdlib.h>
#include "lds_utils.h"
#include "lds_uart.h"
#include "lds_mic.h"
#include "lds_led_config.h"
#include "lds_at.h"

#define DBG_TAG "MIC"
// #define DBG_LVL DBG_INFO
#include <rtdbg.h>

#define MIC_VERSION_MAX_LEN     64
/* ================================ Configuration ========================== */
#define MIC_SERIAL_NAME         "uart4"
#define MIC_POWER_CTRL_PIN      "PE.0"
#define MIC_CMD_QUEUE_SIZE      16                           /**< 命令队列大小，支持最多16个待处理命令 */
#define MIC_MAX_ERROR_COUNT     10                           /**< 假定设备未连接前的最大连续错误数 */

/* ================================ 超时配置 ================== */
#define MIC_RESPONSE_TIMEOUT    (RT_TICK_PER_SECOND * 2)    /**< 2秒响应超时 */
#define MIC_HEARTBEAT_TIMEOUT   (RT_TICK_PER_SECOND * 300)   /**< 300秒心跳超时 */
#define MIC_PARSE_TIMEOUT       (RT_TICK_PER_SECOND * 1)   /**< 状态机1秒解析超时 */
#define MIC_RETRY_COUNT         2                           /**< 最大重试次数 */

/* ================================ 全局变量 ======================= */
static rt_base_t g_micPowerCtrl = -1;       /**< 麦克风电源控制引脚 */
static rt_device_t g_micDev = RT_NULL;       /**< UART设备句柄 */
static struct rt_timer g_heartbeatTimer;    /**< 心跳超时定时器 */
static struct rt_timer g_retransmissionTimer; /**< 命令队列重传定时器 */
static struct rt_timer g_parseTimer;        /**< 状态机解析超时定时器 */
static struct rt_mutex g_micMutex;          /**< 线程安全互斥锁 */
static uint8_t g_errorCount = 0 ;       /**< 连续错误计数 */
static bool g_enable = true;            /**< 麦克风通信使能标志 */

/* ================================ 协议状态机 ================ */
static lds_mic_frame_t g_rxFrame;           /**< 当前接收帧 */
static uint8_t g_rxBuffer[MIC_MAX_FRAME_LEN]; /**< 帧接收缓冲区 */
static uint16_t g_rxIndex = 0;              /**< 当前接收索引 */
static uint8_t g_currentSeq = 0;            /**< 当前序列号 */

/* ================================ 命令队列管理 ================ */
/**
 * @brief 待处理命令队列条目结构体
 * @details 包含等待ACK的命令的所有信息，包括重试管理。
 */
typedef struct {
    bool active;                            /**< 定义此队列槽是否在使用 */
    uint8_t seq;                            /**< 此命令的唯一序列号 */
    uint16_t modelId;                       /**< 命令的型号ID */
    uint16_t cmd;                           /**< 命令码 */
    uint8_t addr;                           /**< 目标设备地址 */
    uint16_t dataLen;                       /**< 数据载荷长度 */
    uint8_t data[MIC_MAX_DATA_LEN];         /**< 数据载荷 */
    uint8_t retryCount;                     /**< 当前重试计数 */
    rt_tick_t sent_timestamp;               /**< 命令最后发送时的系统时钟 */
} lds_mic_cmd_queue_entry_t;

static lds_mic_cmd_queue_entry_t g_cmdQueue[MIC_CMD_QUEUE_SIZE]; /**< 命令队列 */
static void ldsMicStartRetransmissionTimer(void);
static int ldsMicSendFrame(uint16_t modelId, uint16_t cmd, uint8_t addr,
                          const uint8_t *data, uint16_t dataLen);
static void ldsMicResetStateMachine(void);

static int8_t mic_status_main_mute = -1;
static int8_t mic_status_sub_mute = -1;
static int8_t mic_status_sound_mode = -1;
static char mic_version[MIC_VERSION_MAX_LEN] = {0};

/**
 * @brief 麦克风协议状态枚举
 * @details 定义协议帧解析状态机的状态
 */
typedef enum {
    MIC_STATE_IDLE = 0,        /**< 等待帧头 */
    MIC_STATE_MODEL_ID_H,      /**< 接收型号ID高字节 */
    MIC_STATE_MODEL_ID_L,      /**< 接收型号ID低字节 */
    MIC_STATE_CMD_H,           /**< 接收命令高字节 */
    MIC_STATE_CMD_L,           /**< 接收命令低字节 */
    MIC_STATE_SEQ,             /**< 接收序列号 */
    MIC_STATE_ADDR,            /**< 接收地址 */
    MIC_STATE_LEN_H,           /**< 接收长度高字节 */
    MIC_STATE_LEN_L,           /**< 接收长度低字节 */
    MIC_STATE_DATA,            /**< 接收数据载荷 */
    MIC_STATE_CHECKSUM,        /**< 接收校验和 */
} LDS_MIC_STATE_E;

static LDS_MIC_STATE_E g_rxState = MIC_STATE_IDLE;

static void ldsMicHandleArrayParams(uint8_t paramType, uint8_t paramValue)
{
    switch (paramType) {
        case LDS_MIC_PARAM_MUTE_CONTROL:
            LOG_I("Mute set to %d", paramValue);
            if((mic_status_main_mute != paramValue) && (g_rxFrame.addr == LDS_MIC_ADDR_HOST)){
                mic_status_main_mute = paramValue;
                if(paramValue != LDS_MIC_MUTE_ON){
                    ldsLedOff(ldsLedGetConfigPin(LED_MAIN_MUTE));
                } else {
                    ldsLedOn(ldsLedGetConfigPin(LED_MAIN_MUTE));
                }
            } else if ((mic_status_sub_mute != paramValue) && (g_rxFrame.addr == LDS_MIC_ADDR_SLAVE_1)) {
                mic_status_sub_mute = paramValue;
                if(paramValue != LDS_MIC_MUTE_ON){
                    ldsLedOff(ldsLedGetConfigPin(LED_SUB_MUTE));
                } else {
                    ldsLedOn(ldsLedGetConfigPin(LED_SUB_MUTE));
                }
            }
            break;
        case LDS_MIC_PARAM_SOUND_MODE:
            LOG_I("Sound mode set to %d", paramValue);
            if(g_rxFrame.addr != LDS_MIC_ADDR_HOST){
                LOG_E("Invalid address for sound mode: %d", g_rxFrame.addr);
                break;
            }
            if(paramValue != mic_status_sound_mode){
                mic_status_sound_mode = paramValue;
                if(paramValue == LDS_MIC_SOUND_MODE_STANDARD){
                    ldsLedOn(ldsLedGetConfigPin(LED_STD));
                    ldsLedOff(ldsLedGetConfigPin(LED_BOY));
                    ldsLedOff(ldsLedGetConfigPin(LED_GIRL));
                }
                else if(paramValue == LDS_MIC_SOUND_MODE_FEMALE){
                    ldsLedOn(ldsLedGetConfigPin(LED_GIRL));
                    ldsLedOff(ldsLedGetConfigPin(LED_BOY));
                    ldsLedOff(ldsLedGetConfigPin(LED_STD));
                }
                else if(paramValue == LDS_MIC_SOUND_MODE_MALE){
                    ldsLedOn(ldsLedGetConfigPin(LED_BOY));
                    ldsLedOff(ldsLedGetConfigPin(LED_STD));
                    ldsLedOff(ldsLedGetConfigPin(LED_GIRL));
                }
            }
            break;
        default:
            LOG_E("Invalid parameter type: %d", paramType);
            break;
    }
}

/**
 * @brief 重置麦克风设备
 * @details 通过电源控制引脚执行麦克风设备的硬件重置
 */
void ldsMicReset(void)
{
    if (g_micPowerCtrl <= 0) {
        LOG_E("MIC power control pin not initialized");
        return;
    }

    LOG_I("Resetting freeless mic");
    rt_pin_write(g_micPowerCtrl, PIN_LOW);
    rt_thread_mdelay(500);
    rt_pin_write(g_micPowerCtrl, PIN_HIGH);
    rt_thread_mdelay(100);
}

/**
 * @brief 心跳超时处理函数
 * @details 当心跳超时发生时调用，触发设备重置
 *
 * @param parameter 定时器参数（未使用）
 */
static void ldsMicHeartbeatTimeout(void *parameter)
{
    LOG_W("heartbeat timeout");
    // 重启会导致mic音效恢复默认
    // ldsMicReset();
    ldsMicSendFrame(LDS_MIC_MODEL_ARRAY_MIC, LDS_MIC_CMD_VERSION_QUERY, LDS_MIC_ADDR_HOST, RT_NULL, 0);
}

/**
 * @brief 解析超时处理函数
 * @details 当解析超时发生时调用，重置状态机以防止挂起
 *
 * @param parameter 定时器参数（未使用）
 */
static void ldsMicParseTimeout(void *parameter)
{
    LOG_W("Parse timeout state %d, reset", g_rxState);
    ldsMicResetStateMachine();
}

/**
 * @brief 初始化命令队列
 * @details 清除命令队列中的所有条目，将它们标记为非活动状态。
 */
static void ldsMicInitCmdQueue(void)
{
    rt_memset(&g_cmdQueue, 0, sizeof(g_cmdQueue));
}

/**
 * @brief 查找队列中最旧的待处理命令。
 * @details 遍历队列以查找具有最早发送时间戳的活动命令。
 *          此命令被视为重传的"头部"。
 * @return 指向最旧命令条目的指针，如果队列为空则返回RT_NULL。
 */
static lds_mic_cmd_queue_entry_t* ldsMicFindOldestCmd(void)
{
    lds_mic_cmd_queue_entry_t *oldest_cmd = RT_NULL;
    rt_tick_t min_timestamp = RT_TICK_MAX;

    for (int i = 0; i < MIC_CMD_QUEUE_SIZE; i++) {
        if (g_cmdQueue[i].active) {
            if (oldest_cmd == RT_NULL || (g_cmdQueue[i].sent_timestamp < min_timestamp)) {
                min_timestamp = g_cmdQueue[i].sent_timestamp;
                oldest_cmd = &g_cmdQueue[i];
            }
        }
    }
    return oldest_cmd;
}

/**
 * @brief 重传超时处理函数。
 * @details 当重传定时器到期时调用此函数。它处理队列中最旧命令的
 *          重传或丢弃。
 * @param parameter 未使用。
 */
static void ldsMicRetransmissionTimeout(void *parameter)
{
    uint8_t seq_bk = 0;
    rt_mutex_take(&g_micMutex, RT_WAITING_FOREVER);

    lds_mic_cmd_queue_entry_t *cmd_to_retry = ldsMicFindOldestCmd();

    if (cmd_to_retry != RT_NULL) {
        if (cmd_to_retry->retryCount >= MIC_RETRY_COUNT) {
            LOG_E("Max retries for cmd=0x%04X, seq=%d. Dropping.", cmd_to_retry->cmd, cmd_to_retry->seq);
            cmd_to_retry->active = false; // Drop the command
            g_errorCount ++;
            if(g_errorCount > MIC_MAX_ERROR_COUNT){
                LOG_W("mic reach max error count,most likely not connected");
                g_enable = false;
                // rt_timer_stop(&g_heartbeatTimer);
            }
        } else {
            cmd_to_retry->retryCount++;
            LOG_W("Retrying cmd 0x%04X, seq=%d, attempt %d/%d",
                  cmd_to_retry->cmd, cmd_to_retry->seq, cmd_to_retry->retryCount, MIC_RETRY_COUNT);

            // 使用相同的序列号重新发送命令
            seq_bk = g_currentSeq;
            g_currentSeq = cmd_to_retry->seq - 1; // ldsMicSendFrame会将其递增回来
            ldsMicSendFrame(cmd_to_retry->modelId, cmd_to_retry->cmd, cmd_to_retry->addr,
                              cmd_to_retry->data, cmd_to_retry->dataLen);
            cmd_to_retry->sent_timestamp = rt_tick_get();
            g_currentSeq = seq_bk;
        }
    }

    // 处理后，始终尝试为下一个待处理命令重启定时器
    ldsMicStartRetransmissionTimer();

    rt_mutex_release(&g_micMutex);
}

/**
 * @brief 如果有待处理命令，则启动重传定时器。
 * @details 查找最旧的命令并为其设置单次定时器。
 *          必须在持有互斥锁的情况下调用此函数。
 */
static void ldsMicStartRetransmissionTimer(void)
{
    rt_timer_stop(&g_retransmissionTimer);

    lds_mic_cmd_queue_entry_t *next_cmd = ldsMicFindOldestCmd();
    if (next_cmd != RT_NULL) {
        rt_tick_t timeout_tick = MIC_RESPONSE_TIMEOUT;
        // 可选：如果需要可以计算剩余时间，但固定超时更简单。
        rt_timer_control(&g_retransmissionTimer, RT_TIMER_CTRL_SET_TIME, &timeout_tick);
        rt_timer_start(&g_retransmissionTimer);
    }
}

/**
 * @brief 获取下一个序列号
 * @details 为发出的帧生成下一个序列号
 *
 * @return uint8_t 下一个序列号（0x00-0xFF）
 */
static uint8_t ldsMicGetNextSeq(void)
{
    return ++g_currentSeq;
}

/**
 * @brief 发送协议帧
 * @details 构造并发送带有校验和的完整协议帧。
 *          注意：这是低级发送函数，不管理队列。
 *
 * @param modelId 设备型号ID（大端序）
 * @param cmd 命令码（大端序）
 * @param addr 目标地址
 * @param data 指向数据载荷的指针（如果dataLen为0可以为NULL）
 * @param dataLen 数据载荷长度
 * @return int 成功时返回0，失败时返回负错误码
 */
static int ldsMicSendFrame(uint16_t modelId, uint16_t cmd, uint8_t addr,
                          const uint8_t *data, uint16_t dataLen)
{
    uint8_t frame[MIC_MAX_FRAME_LEN];
    uint16_t frameLen;
    uint8_t checksum;
    rt_size_t written;
    uint8_t seq = 0;
    
    // if(!g_enable){
    //     LOG_D("MIC not enabled");
    //     return -RT_ERROR;
    // }
    if(ldsAtGetFactoryTestMode()){
        return 0;
    }

    seq = ldsMicGetNextSeq();

    if (g_micDev == RT_NULL) {
        LOG_E("MIC device not initialized");
        return -RT_ERROR;
    }

    if (dataLen > MIC_MAX_DATA_LEN) {
        LOG_E("Data length %d exceeds maximum %d", dataLen, MIC_MAX_DATA_LEN);
        return -RT_EINVAL;
    }

    /* 构造帧 */
    frameLen = 0;
    frame[frameLen++] = MIC_FRAME_HEAD;                   /* 帧头 */
    frame[frameLen++] = (uint8_t)(modelId >> 8);         /* 型号ID高字节 */
    frame[frameLen++] = (uint8_t)(modelId & 0xFF);       /* 型号ID低字节 */
    frame[frameLen++] = (uint8_t)(cmd >> 8);             /* 命令高字节 */
    frame[frameLen++] = (uint8_t)(cmd & 0xFF);           /* 命令低字节 */
    frame[frameLen++] = seq;                              /* 序列号 */
    frame[frameLen++] = addr;                             /* 地址 */
    frame[frameLen++] = (uint8_t)(dataLen >> 8);         /* 长度高字节 */
    frame[frameLen++] = (uint8_t)(dataLen & 0xFF);       /* 长度低字节 */

    /* 复制数据载荷 */
    if (data != RT_NULL && dataLen > 0) {
        rt_memcpy(&frame[frameLen], data, dataLen);
        frameLen += dataLen;
    }

    /* 计算并追加校验和 */
    checksum = ldsUtilCheckSum(frame, frameLen);
    frame[frameLen++] = checksum;

    /* 发送帧 */
    written = rt_device_write(g_micDev, 0, frame, frameLen);
    if (written != frameLen) {
        LOG_E("Failed to send complete frame, sent %d of %d bytes", written, frameLen);
        return -RT_ERROR;
    }

    LOG_D("Sent frame: modelId=0x%04X, cmd=0x%04X, seq=%d, addr=0x%02X, len=%d",
          modelId, cmd, seq, addr, dataLen);
    // LOG_HEX("mic-tx", 16, frame, frameLen);

    return seq; // 返回使用的序列号
}

/**
 * @brief 发送命令并将其添加到待处理队列。
 * @details 这是发送命令的新主函数。它在队列中查找空闲槽位，
 *          发送帧，并管理重传定时器。
 * @param modelId 设备型号ID
 * @param cmd 命令码
 * @param addr 目标地址
 * @param data 指向数据载荷的指针
 * @param dataLen 数据载荷长度
 * @return 成功时返回0，失败时返回负错误码。
 */
static int ldsMicSendCommand(uint16_t modelId, uint16_t cmd, uint8_t addr,
                               const uint8_t *data, uint16_t dataLen)
{
    if(ldsAtGetFactoryTestMode()){
        return 0;
    }

    if(!g_enable){
        LOG_D("MIC not enabled");
        return -RT_ERROR;
    }

    rt_err_t result = rt_mutex_take(&g_micMutex, RT_WAITING_FOREVER);
    if (result != RT_EOK) {
        LOG_E("Failed to acquire mutex: %d", result);
        return -RT_ERROR;
    }

    int free_slot_idx = -1;
    for (int i = 0; i < MIC_CMD_QUEUE_SIZE; i++) {
        if (!g_cmdQueue[i].active) {
            free_slot_idx = i;
            break;
        }
    }

    if (free_slot_idx == -1) {
        LOG_E("Command queue is full. Cannot send cmd 0x%04X.", cmd);
        rt_mutex_release(&g_micMutex);
        return -RT_EBUSY;
    }

    // 备份当前序列号，因为ldsMicSendFrame会修改它。
    uint8_t seq_bak = g_currentSeq;
    int seq_sent = ldsMicSendFrame(modelId, cmd, addr, data, dataLen);

    if (seq_sent < 0) {
        g_currentSeq = seq_bak; // 失败时恢复序列号
        rt_mutex_release(&g_micMutex);
        return seq_sent; // 传播错误
    }

    // 填充队列条目
    lds_mic_cmd_queue_entry_t *entry = &g_cmdQueue[free_slot_idx];
    entry->active = true;
    entry->seq = (uint8_t)seq_sent;
    entry->modelId = modelId;
    entry->cmd = cmd;
    entry->addr = addr;
    entry->dataLen = dataLen;
    if (dataLen > 0) {
        rt_memcpy(entry->data, data, dataLen);
    }
    entry->retryCount = 0;
    entry->sent_timestamp = rt_tick_get();

    LOG_D("Cmd 0x%04X with seq=%d added to queue.", cmd, entry->seq);

    // 如果定时器未运行（即队列为空），则启动它。
    rt_uint8_t timer_state = 0;
    rt_timer_control(&g_retransmissionTimer, RT_TIMER_CTRL_GET_STATE, &timer_state);
    if (timer_state == RT_TIMER_FLAG_DEACTIVATED) {
        ldsMicStartRetransmissionTimer();
    }

    rt_mutex_release(&g_micMutex);
    return 0;
}


/**
 * @brief 处理接收到的协议帧
 * @details 处理完整的接收帧并根据命令类型进行分发
 *
 * @param frame 指向接收帧结构的指针
 * @return int 成功时返回0，失败时返回负错误码
 */
static int ldsMicProcessFrame(const lds_mic_frame_t *frame)
{
    if (frame == RT_NULL) {
        LOG_E("Invalid frame pointer");
        return -RT_EINVAL;
    }

    LOG_D("Processing frame: modelId=0x%04X, cmd=0x%04X, seq=%d, addr=0x%02X, len=%d",
          frame->modelId, frame->cmd, frame->seq, frame->addr, frame->dataLen);

    /* 在任何有效帧上重置心跳定时器 */
    rt_timer_stop(&g_heartbeatTimer);
    rt_timer_start(&g_heartbeatTimer);

    g_errorCount = 0;
    g_enable = true;
    // 这是一个ACK或响应帧。尝试将其与待处理命令匹配。
    rt_mutex_take(&g_micMutex, RT_WAITING_FOREVER);
    bool ack_matched = false;
    for (int i = 0; i < MIC_CMD_QUEUE_SIZE; i++) {
        if (g_cmdQueue[i].active && g_cmdQueue[i].seq == frame->seq) {
            LOG_D("ACK received for seq=%d. Removing from queue.", frame->seq);
            g_cmdQueue[i].active = false; // 停用命令
            ack_matched = true;

            // 命令已被确认。我们需要检查是否应该为下一个最旧的命令重启定时器。
            ldsMicStartRetransmissionTimer();
            break;
        }
    }
    if (!ack_matched) {
         LOG_W("Received ACK for unexpected seq=%d or command type 0x%04X", frame->seq, frame->cmd);
    }
    rt_mutex_release(&g_micMutex);


    switch (frame->cmd) {
        case LDS_MIC_CMD_FACTORY_RESET:
            LOG_I("Factory reset command received");
            break;

        case LDS_MIC_CMD_REBOOT:
            LOG_I("Reboot command response received");
            break;

        case LDS_MIC_CMD_VERSION_QUERY:
            if (frame->dataLen > 0) {
                int max_len = frame->dataLen > MIC_VERSION_MAX_LEN - 1 ? MIC_VERSION_MAX_LEN - 1 : frame->dataLen;
                LOG_I("Version info received: %.*s", frame->dataLen, frame->data);
                rt_memcpy(mic_version, frame->data, max_len);
                mic_version[max_len] = '\0';
            }
            break;

        case LDS_MIC_CMD_SET_ARRAY_PARAMS:
            LOG_I("Array parameters received");
            if (frame->dataLen >= 2) {
                uint8_t paramType = frame->data[0];
                uint8_t paramValue = frame->data[1];
                LOG_I("Parameter type=0x%02X, value=0x%02X", paramType, paramValue);
                ldsMicHandleArrayParams(paramType, paramValue);
            }
            break;

        default:
            LOG_W("Unknown command received: 0x%04X", frame->cmd);
            return -RT_ERROR;
    }

    return 0;
}

/**
 * @brief 启动解析超时定时器
 * @details 启动或重启解析超时定时器以防止状态机挂起
 */
static void ldsMicStartParseTimer(void)
{
    rt_timer_stop(&g_parseTimer);
    rt_timer_start(&g_parseTimer);
}

/**
 * @brief 重置帧解析状态机
 * @details 将状态机重置为空闲状态并清除缓冲区
 */
static void ldsMicResetStateMachine(void)
{
    /* 停止解析超时定时器 */
    rt_timer_stop(&g_parseTimer);

    g_rxState = MIC_STATE_IDLE;
    g_rxIndex = 0;
    rt_memset(&g_rxFrame, 0, sizeof(g_rxFrame));
    rt_memset(g_rxBuffer, 0, sizeof(g_rxBuffer));
}

/**
 * @brief 协议帧解析状态机
 * @details 根据协议规范解析传入的字节
 *
 * @param data 指向接收数据缓冲区的指针
 * @param size 接收数据的大小
 * @return int 成功时返回0，失败时返回负错误码
 */
static int ldsMicParseData(const uint8_t *data, rt_size_t size)
{
    if (data == RT_NULL || size == 0) {
        LOG_E("Invalid data or size");
        return -RT_EINVAL;
    }

    for (rt_size_t i = 0; i < size; i++) {
        uint8_t byte = data[i];
        // LOG_D("state %d", g_rxState);
        switch (g_rxState) {
            case MIC_STATE_IDLE:
                if (byte == MIC_FRAME_HEAD) {
                    ldsMicResetStateMachine();
                    g_rxBuffer[g_rxIndex++] = byte;
                    g_rxFrame.head = byte;
                    g_rxState = MIC_STATE_MODEL_ID_H;
                    /* 进入解析状态时启动解析超时定时器 */
                    ldsMicStartParseTimer();
                }
                break;

            case MIC_STATE_MODEL_ID_H:
                g_rxBuffer[g_rxIndex++] = byte;
                g_rxFrame.modelId = (uint16_t)(byte << 8);
                g_rxState = MIC_STATE_MODEL_ID_L;
                /* 在每个有效字节上重启解析超时定时器 */
                ldsMicStartParseTimer();
                break;

            case MIC_STATE_MODEL_ID_L:
                g_rxBuffer[g_rxIndex++] = byte;
                g_rxFrame.modelId |= byte;
                g_rxState = MIC_STATE_CMD_H;
                ldsMicStartParseTimer();
                break;

            case MIC_STATE_CMD_H:
                g_rxBuffer[g_rxIndex++] = byte;
                g_rxFrame.cmd = (uint16_t)(byte << 8);
                g_rxState = MIC_STATE_CMD_L;
                ldsMicStartParseTimer();
                break;

            case MIC_STATE_CMD_L:
                g_rxBuffer[g_rxIndex++] = byte;
                g_rxFrame.cmd |= byte;
                g_rxState = MIC_STATE_SEQ;
                ldsMicStartParseTimer();
                break;

            case MIC_STATE_SEQ:
                g_rxBuffer[g_rxIndex++] = byte;
                g_rxFrame.seq = byte;
                g_rxState = MIC_STATE_ADDR;
                ldsMicStartParseTimer();
                break;

            case MIC_STATE_ADDR:
                g_rxBuffer[g_rxIndex++] = byte;
                g_rxFrame.addr = byte;
                g_rxState = MIC_STATE_LEN_H;
                ldsMicStartParseTimer();
                break;

            case MIC_STATE_LEN_H:
                g_rxBuffer[g_rxIndex++] = byte;
                g_rxFrame.dataLen = (uint16_t)(byte << 8);
                g_rxState = MIC_STATE_LEN_L;
                ldsMicStartParseTimer();
                break;

            case MIC_STATE_LEN_L:
                g_rxBuffer[g_rxIndex++] = byte;
                g_rxFrame.dataLen |= byte;

                /* 验证数据长度 */
                if (g_rxFrame.dataLen > MIC_MAX_DATA_LEN) {
                    LOG_E("Invalid data length: %d", g_rxFrame.dataLen);
                    ldsMicResetStateMachine();
                    break;
                }

                if (g_rxFrame.dataLen == 0) {
                    g_rxState = MIC_STATE_CHECKSUM;
                } else {
                    g_rxState = MIC_STATE_DATA;
                }
                rt_memset(g_rxFrame.data, 0, sizeof(g_rxFrame.data));
                ldsMicStartParseTimer();
                break;

            case MIC_STATE_DATA:
                g_rxBuffer[g_rxIndex++] = byte;
                g_rxFrame.data[g_rxIndex - 10] = byte;  /* 数据从索引9开始，+1用于校验和 */

                if (g_rxIndex >= (9 + g_rxFrame.dataLen)) {
                    g_rxState = MIC_STATE_CHECKSUM;
                }
                ldsMicStartParseTimer();
                break;

            case MIC_STATE_CHECKSUM:
                g_rxFrame.checksum = byte;
                g_rxBuffer[g_rxIndex++] = byte; // 同时将校验和添加到缓冲区以进行验证

                /* 验证校验和 */
                uint8_t calculatedChecksum = ldsUtilCheckSum(g_rxBuffer, g_rxIndex - 1);
                if (calculatedChecksum != g_rxFrame.checksum) {
                    LOG_E("Checksum mismatch: calculated=0x%02X, received=0x%02X",
                          calculatedChecksum, g_rxFrame.checksum);
                    LOG_HEX("mic-rx-err", 16, g_rxBuffer, g_rxIndex);
                    ldsMicResetStateMachine();
                    break;
                }

                // LOG_HEX("mic-rx", 16, g_rxBuffer, g_rxIndex);
                ldsMicProcessFrame(&g_rxFrame);
                ldsMicResetStateMachine();
                break;

            default:
                LOG_E("Invalid state: %d", g_rxState);
                ldsMicResetStateMachine();
                break;
        }

        /* 防止缓冲区溢出 */
        if (g_rxIndex >= MIC_MAX_FRAME_LEN) {
            LOG_E("Frame buffer overflow");
            ldsMicResetStateMachine();
            break;
        }
    }

    return 0;
}

/**
 * @brief UART数据处理回调函数
 * @details 向UART驱动程序注册的用于数据处理的回调函数
 *
 * @param dev RT-Thread设备句柄
 * @param data 指向接收数据缓冲区的指针
 * @param size 接收数据的字节大小
 * @return int 成功时返回0，失败时返回负错误码
 */
int ldsMicProcess(rt_device_t dev, const uint8_t *data, rt_size_t size)
{
    if (data == RT_NULL || size == 0) {
        LOG_E("Invalid data or size");
        return -RT_EINVAL;
    }

    if (dev == RT_NULL) {
        LOG_E("Invalid device handle");
        return -RT_EINVAL;
    }

    LOG_D("Received %d bytes from %s", size, dev->parent.name);

    if(ldsAtGetFactoryTestMode()){
        rt_device_write(g_micDev, 0, data, size);
        return 0;
    }
    // 这里没有互斥锁，解析在UART上下文中进行。
    // 互斥锁在处理/确认逻辑内部使用。
    int ret = ldsMicParseData(data, size);

    return ret;
}

/* ================================ 公共API函数 =================== */
uint8_t ldsMicGetStatus(void)
{
    uint8_t status = 0;
    status |= mic_status_main_mute == 1 ? 0x01 : 0x00;
    status |= mic_status_sub_mute == 1 ? 0x02 : 0x00;
    status |= (mic_status_sound_mode == -1 ? 0 : mic_status_sound_mode) >> 2;
    return status;
}
int ldsMicGetPowerCtrl(void)
{
    return rt_pin_read(g_micPowerCtrl);
}
int ldsMicSetPowerCtrl(int on)
{
    if (g_micPowerCtrl <= 0) {
        LOG_E("MIC power control pin not initialized");
        return -RT_ERROR;
    }

    rt_pin_write(g_micPowerCtrl, on ? PIN_HIGH : PIN_LOW);
    g_enable = on;
    if(!on){
        g_errorCount = MIC_MAX_ERROR_COUNT;
        rt_timer_stop(&g_heartbeatTimer);
    } else {
        g_errorCount = 0;
        rt_timer_stop(&g_heartbeatTimer);
        rt_timer_start(&g_heartbeatTimer);
    }
    return 0;
}

const char *ldsMicGetVersion(void)
{
    return mic_version;
}

int ldsMicQueryVersion(void)
{
    return ldsMicSendCommand(LDS_MIC_MODEL_ARRAY_MIC, LDS_MIC_CMD_VERSION_QUERY, LDS_MIC_ADDR_HOST, RT_NULL, 0);
}

int ldsMicQueryStatus(void)
{
    ldsMicSetMuteControl(LDS_MIC_ADDR_HOST, LDS_MIC_MUTE_QUERY);
    rt_thread_mdelay(20);
    ldsMicSetMuteControl(LDS_MIC_ADDR_SLAVE_1, LDS_MIC_MUTE_QUERY);
    rt_thread_mdelay(20);
    ldsMicSetSoundMode(LDS_MIC_SOUND_MODE_QUERY);
    return 0;
}

int ldsMicSetMuteByAddr(uint8_t addr)
{
    LDS_MIC_MUTE_E mute = LDS_MIC_MUTE_ON;
    if(LDS_MIC_ADDR_HOST == addr){
        mute =  mic_status_main_mute ? LDS_MIC_MUTE_OFF : LDS_MIC_MUTE_ON;
    } else {
        mute =  mic_status_sub_mute ? LDS_MIC_MUTE_OFF : LDS_MIC_MUTE_ON;
    }
    LOG_D("addr %02x mute %d", addr, mute);
    return ldsMicSetMuteControl(addr, mute);
}

int ldsMicSetMuteControl(uint8_t addr, LDS_MIC_MUTE_E muteState)
{
    int ret = 0;
    uint8_t data[2];

    if (muteState >= LDS_MIC_MUTE_MAX) {
        LOG_E("Invalid mute state: %d", muteState);
        return -RT_EINVAL;
    }

    data[0] = LDS_MIC_PARAM_MUTE_CONTROL;
    data[1] = (uint8_t)muteState;
    
    ret = ldsMicSendCommand(LDS_MIC_MODEL_ARRAY_MIC, LDS_MIC_CMD_SET_ARRAY_PARAMS, addr, data, sizeof(data));

    if(!ret && (muteState < LDS_MIC_MUTE_QUERY)){
        if(LDS_MIC_ADDR_HOST == addr) {
            mic_status_main_mute = muteState;
        } else {
            mic_status_sub_mute = muteState;
        }
    }
    return ret;
}

int ldsMicSetSoundMode(LDS_MIC_SOUND_MODE_E soundMode)
{
    int ret = 0;
    uint8_t data[2];

    if (soundMode >= LDS_MIC_SOUND_MODE_MAX) {
        LOG_E("Invalid sound mode: %d", soundMode);
        return -RT_EINVAL;
    }

    data[0] = LDS_MIC_PARAM_SOUND_MODE;
    data[1] = (uint8_t)soundMode;

    ret = ldsMicSendCommand(LDS_MIC_MODEL_ARRAY_MIC, LDS_MIC_CMD_SET_ARRAY_PARAMS, LDS_MIC_ADDR_HOST, data, sizeof(data));
    if(!ret && (soundMode < LDS_MIC_SOUND_MODE_QUERY)){
        mic_status_sound_mode = soundMode;
    }
    return ret;
}

/**
 * @brief 初始化麦克风通信系统
 * @details 初始化硬件、UART通信、定时器和状态机
 *
 * @return int 成功时返回0，失败时返回负错误码
 *
 * @note 此函数执行完整的麦克风系统初始化，包括：
 *       - 电源控制引脚设置
 *       - 带回调的UART接口初始化
 *       - 心跳和重传的定时器配置
 *       - 线程安全的互斥锁初始化
 *       - 状态机和命令队列初始化
 */
int ldsMicInit(void)
{
    rt_err_t result;

    /* 为线程安全初始化互斥锁 */
    result = rt_mutex_init(&g_micMutex, "mic_mutex", RT_IPC_FLAG_PRIO);
    if (result != RT_EOK) {
        LOG_E("Failed to initialize mutex: %d", result);
        return -RT_ERROR;
    }

    /* 初始化电源控制引脚 */
    g_micPowerCtrl = power_ctrl_pin_init(MIC_POWER_CTRL_PIN, PIN_HIGH);
    if (g_micPowerCtrl < 0) {
        LOG_E("Failed to initialize MIC power control pin %s", MIC_POWER_CTRL_PIN);
        rt_mutex_detach(&g_micMutex);
        return -RT_ERROR;
    }

    /* 使用回调初始化UART */
    g_micDev = ldsUartInit(MIC_SERIAL_NAME, LDS_UART_INDEX_4, ldsMicProcess);
    if (g_micDev == RT_NULL) {
        LOG_E("Failed to initialize MIC UART %s", MIC_SERIAL_NAME);
        rt_mutex_detach(&g_micMutex);
        return -RT_ERROR;
    }

    /* 初始化心跳定时器 */
    rt_timer_init(&g_heartbeatTimer, "mic_hb",
                  ldsMicHeartbeatTimeout,
                  RT_NULL,
                  MIC_HEARTBEAT_TIMEOUT,
                  RT_TIMER_FLAG_SOFT_TIMER | RT_TIMER_FLAG_PERIODIC);
    rt_timer_start(&g_heartbeatTimer);

    /* 初始化重传定时器 */
    rt_timer_init(&g_retransmissionTimer, "mic_retry",
                  ldsMicRetransmissionTimeout,
                  RT_NULL,
                  MIC_RESPONSE_TIMEOUT,
                  RT_TIMER_FLAG_SOFT_TIMER | RT_TIMER_FLAG_ONE_SHOT);

    /* 初始化解析超时定时器 */
    rt_timer_init(&g_parseTimer, "mic_parse",
                  ldsMicParseTimeout,
                  RT_NULL,
                  MIC_PARSE_TIMEOUT,
                  RT_TIMER_FLAG_SOFT_TIMER | RT_TIMER_FLAG_ONE_SHOT);

    /* 初始化状态机 */
    ldsMicResetStateMachine();
    g_currentSeq = 0;

    /* 初始化命令队列 */
    ldsMicInitCmdQueue();


    LOG_I("Microphone communication system initialized successfully");
    return 0;
}

/**
 * @brief 反初始化麦克风通信系统
 * @details 清理所有资源并停止通信
 *
 * @return int 成功时返回0，失败时返回负错误码
 */
int ldsMicDeinit(void)
{
    /* 停止定时器 */
    rt_timer_stop(&g_heartbeatTimer);
    rt_timer_stop(&g_retransmissionTimer);
    rt_timer_stop(&g_parseTimer);
    rt_timer_detach(&g_heartbeatTimer);
    rt_timer_detach(&g_retransmissionTimer);
    rt_timer_detach(&g_parseTimer);

    /* 重置状态机 */
    ldsMicResetStateMachine();

    /* 清除命令队列 */
    rt_mutex_take(&g_micMutex, RT_WAITING_FOREVER);
    ldsMicInitCmdQueue();
    rt_mutex_release(&g_micMutex);

    /* 关闭UART设备 */
    if (g_micDev != RT_NULL) {
        rt_device_close(g_micDev);
        g_micDev = RT_NULL;
    }

    /* 关闭设备电源 */
    if (g_micPowerCtrl > 0) {
        rt_pin_write(g_micPowerCtrl, PIN_LOW);
        g_micPowerCtrl = -1;
    }

    /* 清理互斥锁 */
    rt_mutex_detach(&g_micMutex);

    LOG_I("Microphone communication system deinitialized");
    return 0;
}

/* ================================ MSH调试命令 ===================== */
#ifdef RT_USING_FINSH

static int ldsMicQueueStatus(void)
{
    rt_kprintf("Command Queue Status (Size: %d):\n", MIC_CMD_QUEUE_SIZE);
    bool empty = true;
    for (int i = 0; i < MIC_CMD_QUEUE_SIZE; i++) {
        if (g_cmdQueue[i].active) {
            empty = false;
            rt_kprintf("  Slot %d: [ACTIVE]\n", i);
            rt_kprintf("    seq: %d, cmd: 0x%04X, addr: 0x%02X\n",
                       g_cmdQueue[i].seq, g_cmdQueue[i].cmd, g_cmdQueue[i].addr);
            rt_kprintf("    retries: %d, sent_at: %u\n",
                       g_cmdQueue[i].retryCount, g_cmdQueue[i].sent_timestamp);
        }
    }
    if (empty) {
        rt_kprintf("  Queue is empty.\n");
    }
    return 0;
}


/**
 * @brief 麦克风操作的MSH命令
 * @details 为测试麦克风通信提供命令行接口
 *
 * @param argc 参数计数
 * @param argv 参数向量
 * @return int 成功时返回0，失败时返回负错误码
 */
static int ldsMicCmd(int argc, char **argv)
{
    if (argc < 2) {
        rt_kprintf("Usage: mic <command> [args...]\n");
        rt_kprintf("Commands:\n");
        rt_kprintf("  init                    - Initialize microphone system\n");
        rt_kprintf("  deinit                  - Deinitialize microphone system\n");
        rt_kprintf("  reset                   - Reset microphone device\n");
        rt_kprintf("  version                 - Query version information\n");
        rt_kprintf("  mute <addr> <state>     - Set mute control (0=unmute, 1=mute, 255=query)\n");
        rt_kprintf("  sound <mode>            - Set sound mode (0=standard, 1=female, 2=male, 255=query)\n");
        rt_kprintf("  status                  - Show system status and queue\n");
        return 0;
    }

    if (rt_strcmp(argv[1], "init") == 0) {
        int ret = ldsMicInit();
        rt_kprintf("Microphone init %s\n", ret == 0 ? "success" : "failed");
        return ret;
    }

    if (rt_strcmp(argv[1], "deinit") == 0) {
        int ret = ldsMicDeinit();
        rt_kprintf("Microphone deinit %s\n", ret == 0 ? "success" : "failed");
        return ret;
    }

    if (rt_strcmp(argv[1], "reset") == 0) {
        ldsMicReset();
        rt_kprintf("Microphone device reset\n");
        return 0;
    }

    if (rt_strcmp(argv[1], "version") == 0) {
        int ret = ldsMicQueryVersion();
        rt_kprintf("Version query %s\n", ret == 0 ? "sent" : "failed");
        return ret;
    }

    if (rt_strcmp(argv[1], "mute") == 0) {
        if (argc != 4) {
            rt_kprintf("Usage: mic mute <addr> <state>\n");
            rt_kprintf("  state: 0=unmute, 1=mute, 255=query\n");
            return -RT_EINVAL;
        }
        uint8_t addr = (uint8_t)strtoul(argv[2], RT_NULL, 0);
        uint8_t state = (uint8_t)strtoul(argv[3], RT_NULL, 0);
        int ret = ldsMicSetMuteControl(addr, (LDS_MIC_MUTE_E)state);
        rt_kprintf("Mute control command %s\n", ret == 0 ? "sent" : "failed");
        return ret;
    }

    if (rt_strcmp(argv[1], "sound") == 0) {
        if (argc != 3) {
            rt_kprintf("Usage: mic sound <mode>\n");
            rt_kprintf("  mode: 0=standard, 1=female, 2=male, 255=query\n");
            return -RT_EINVAL;
        }
        uint8_t mode = (uint8_t)strtoul(argv[2], RT_NULL, 0);
        int ret = ldsMicSetSoundMode((LDS_MIC_SOUND_MODE_E)mode);
        rt_kprintf("Sound mode command %s\n", ret == 0 ? "sent" : "failed");
        return ret;
    }

    if (rt_strcmp(argv[1], "status") == 0) {
        rt_mutex_take(&g_micMutex, RT_WAITING_FOREVER);
        rt_kprintf("Microphone System Status:\n");
        rt_kprintf("  Device: %s\n", g_micDev ? "initialized" : "not initialized");
        rt_kprintf("  Power Control: %s\n", g_micPowerCtrl > 0 ? "enabled" : "disabled");
        rt_kprintf("  Current Sequence: %d\n", g_currentSeq);
        rt_kprintf("  RX State: %d\n", g_rxState);
        rt_kprintf("  RX Index: %d\n", g_rxIndex);

        /* 检查解析定时器状态 */
        rt_uint8_t parse_timer_state = 0;
        rt_timer_control(&g_parseTimer, RT_TIMER_CTRL_GET_STATE, &parse_timer_state);
        rt_kprintf("  Parse Timer: %s\n", parse_timer_state == RT_TIMER_FLAG_ACTIVATED ? "active" : "inactive");

        ldsMicQueueStatus();
        rt_mutex_release(&g_micMutex);
        return 0;
    }

    rt_kprintf("Unknown command: %s\n", argv[1]);
    return -RT_EINVAL;
}

MSH_CMD_EXPORT_ALIAS(ldsMicCmd, mic, Microphone communication protocol commands);
#endif