
/****************************************************************
lds_bk9532.h
LDS的文件lds_bk9532.h
Huiby
20240201
 ****************************************************************/

#ifndef _LDS_BK9532_H_
#define _LDS_BK9532_H_

#include <stdint.h>
#include <stdbool.h>

#ifdef __cplusplus
extern "C" {
#endif

//BK9532-重要状态
/**
 * @brief BK9532重要状态
 *
 * 此结构体包含BK9532芯片的重要状态。
 *
 * @note
 * - u8Lock:      锁定状态（0: 未锁定, 0x80: 已锁定）
 * - u8UserData:  用户数据
 * - u8RfRssi:    射频RSSI值
 * - u16FltRssi:  滤波后的RSSI值
 * - u16AudRssi:  音频RSSI值
 * - u8Freq[4]:   信道频率值（4字节，大端序）
 */
typedef struct
{
   uint8_t  u8Lock; /**< 锁定状态（0: 未锁定, 0x80: 已锁定） */
   uint8_t  u8UserData; /**< 用户数据 */
   uint8_t  u8RfRssi; /**< 射频RSSI值 */
   uint16_t  u16FltRssi; /**< 滤波后的RSSI值 */
   uint16_t  u16AudRssi; /**< 音频RSSI值 */
   uint8_t  u8Freq[4]; /**< 信道频率值（4字节，大端序） */
} lds_bk9532_status_st;

/* I2C配置结构体 */
typedef struct {
    uint8_t retry_count;            /**< 重试次数 */
    uint8_t retry_delay_base_ms;    /**< 重试间的基础延时 */
    uint8_t retry_delay_max_ms;     /**< 重试间的最大延时 */
    uint8_t reinit_threshold;       /**< 重新初始化的失败阈值 */
    uint16_t timeout_ms;            /**< I2C操作超时时间 */
} lds_bk9532_i2c_config_t;

extern lds_bk9532_status_st sBk9532RegStatus;
extern lds_bk9532_status_st sBk9532RegStatusLast;

#ifdef __cplusplus
}
#endif


#endif
