/**
 * @file lds_dsp.h
 * @brief LDS DSP通信协议栈头文件
 * @details 此头文件包含DSP通信协议函数的声明
 *          和遵循指定大端字节序协议格式的数据结构
 * <AUTHOR> Team
 * @version 1.0
 * @date 2025-07-23
 *
 * @copyright Copyright (c) 2025 LDS
 *
 * 协议格式:
 * Head(2) + CTRL(1) + LEN(1) + DATA(可变) + End(1)
 * - 头部字节: 0xA5 0x5A（按此顺序）
 * - CTRL: 定义操作的控制码
 * - LEN: DATA字段的字节长度（0-59）
 * - DATA: 可变长度载荷数据
 * - 结束字节: 0x16
 * 注意: 除2字节头部外，所有字段都是单字节；不涉及多字节字节序。
 */

#ifndef __APPLICATIONS_LDS_DSP_H__
#define __APPLICATIONS_LDS_DSP_H__

/* ================================ Includes ================================ */
#include <stdint.h>
#include <stdbool.h>
#include <rtthread.h>

#ifdef __cplusplus
extern "C" {
#endif

/* ================================ Macros ================================== */

/* ================================ Protocol Constants ===================== */
#define DSP_FRAME_HEAD1         0xA5
#define DSP_FRAME_HEAD2         0x5A
#define DSP_FRAME_END           0x16
#define DSP_MIN_FRAME_LEN       5      /**< 不含DATA的最小帧长度 */
#define DSP_MAX_FRAME_LEN       64     /**< 最大帧长度 */
#define DSP_MAX_DATA_LEN        (DSP_MAX_FRAME_LEN - DSP_MIN_FRAME_LEN)

/* ================================ 类型定义 ======================== */

/**
 * @brief DSP控制枚举
 * @details 定义DSP通信支持的控制类型
 */
typedef enum {
    LDS_DSP_EFFECT_CTRL_VERSION = 0x0,

    LDS_DSP_EFFECT_CTRL_TREBLE_BASS1 = 0x8E,
    LDS_DSP_EFFECT_CTRL_TREBLE_BASS2 = 0xA4,
    LDS_DSP_EFFECT_CTRL_TREBLE_BASS3 = 0xAC,
    
    LDS_DSP_EFFECT_CTRL_VOLUME1 = 0xA5,
    LDS_DSP_EFFECT_CTRL_VOLUME2 = 0x8F,
    LDS_DSP_EFFECT_CTRL_VOLUME3 = 0xAE,

    LDS_DSP_EFFECT_CTRL_LINE_IN1 = 0x9C,
    LDS_DSP_EFFECT_CTRL_LINE_IN2 = 0x9E,

    LDS_DSP_EFFECT_CTRL_FREELESS_MIC = 0xA9,
} LDS_DSP_EFFECT_CTRL_E;

/**
 * @brief 协议帧结构体
 * @details 表示完整协议帧的结构体
 */
typedef struct {
    uint8_t head1;                           /**< 帧头字节1（0xA5） */
    uint8_t head2;                           /**< 帧头字节2（0x5A） */
    uint8_t ctrl;                            /**< 控制码 */
    uint8_t dataLen;                         /**< 数据长度（字节） */
    uint8_t data[DSP_MAX_DATA_LEN];          /**< 数据载荷 */
    uint8_t end;                             /**< 帧结束（0x16） */
} lds_dsp_frame_t;

/* ================================ 函数声明 =================== */

/**
 * @brief 查询设备版本信息
 * @details 从指定设备请求版本信息
 *
 * @return int 成功返回0，失败返回负错误码
 *
 * @note 响应将在数据字段中包含版本信息
 */
int ldsDspQueryVersion(void);
/**
 * @brief 设置DSP音量
 * @details 设置DSP音量
 *
 * @param volume 音量值
 * @return int 成功返回0，失败返回负错误码
 */
int ldsDspSetVolume(uint8_t volume);
/**
 * @brief 设置DSP高音
 * @details 设置DSP高音
 *
 * @param treble 高音值
 * @return int 成功返回0，失败返回负错误码
 */
int ldsDspSetTreble(uint8_t treble);
/**
 * @brief 设置DSP低音
 * @details 设置DSP低音
 *
 * @param bass 低音值
 * @return int 成功返回0，失败返回负错误码
 */
int ldsDspSetBass(uint8_t bass);
/**
 * @brief 设置DSP线路输入倍数
 * @details 设置DSP线路输入倍数
 *
 * @param line_in1 true设置线路输入1，false设置线路输入2
 * @param high true设置高倍数，false设置低倍数
 * @return int 成功返回0，失败返回负错误码
 */
int ldsDspSetLineInMultiplier(bool line_in1, bool high);
/**
 * @brief 设置DSP线路选择
 * @details 设置DSP线路选择
 *
 * @param on true 手持麦克风优先，false手持麦克风不优先
 * @return int 成功返回0，失败返回负错误码
 */
int ldsDspSetLineSelect(bool on);

/**
 * @brief 初始化DSP通信系统
 * @details 初始化硬件、UART通信、定时器和状态机
 *
 * @return int 成功返回0，失败返回负错误码
 *
 * @note 此函数执行完整的DSP系统初始化，包括：
 *       - 电源控制引脚设置
 *       - 带回调的UART接口初始化
 *       - 心跳和命令重传的定时器配置
 *       - 线程安全的互斥锁初始化
 *       - 状态机和命令队列初始化
 *
 * @example
 * @code
 * int result = ldsDspInit();
 * if (result != 0) {
 *     rt_kprintf("Dsp initialization failed: %d\n", result);
 * }
 * @endcode
 */
int ldsDspInit(void);

/**
 * @brief 反初始化DSP通信系统
 * @details 清理所有资源并停止通信
 *
 * @return int 成功返回0，失败返回负错误码
 *
 * @note 此函数应在系统关闭前或不再需要DSP通信时调用
 */
int ldsDspDeinit(void);

#ifdef __cplusplus
}
#endif

#endif /* __APPLICATIONS_LDS_DSP_H__ */
