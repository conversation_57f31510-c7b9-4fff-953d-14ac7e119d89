#ifndef __APPLICATIONS_LDS_AT_H__
#define __APPLICATIONS_LDS_AT_H__

#include <stdint.h>
#include <stdbool.h>

#ifdef __cplusplus
extern "C" {
#endif

#define LDS_AT_LOG_MAGIC_VALUE      0x5AA5A500
/**
 * @brief 获取工厂测试模式状态
 *
 * @return true 工厂测试模式已启用
 * @return false 工厂测试模式未启用
 */
bool ldsAtGetFactoryTestMode(void);
/**
 * @brief 检查是否设置了通过AT端口输出日志
 *
 * @return 日志过滤级别，0表示静默，仅打印断言
 */
uint8_t ldsAtLogSetted(void);
/**
 * @brief AT命令初始化
 *
 * @return int 0表示成功，其他值表示失败
 */
int ldsAtInit(void);

#ifdef __cplusplus
}
#endif

#endif // !__APPLICATIONS_LDS_AT_H__
