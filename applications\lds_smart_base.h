/**
 * @file lds_smart_base.h
 * @brief LDS智能底座通信协议栈头文件
 * @details 此头文件包含智能底座通信协议函数的声明
 *          和遵循指定大端字节序协议格式的数据结构
 * <AUTHOR> Team
 * @version 1.0
 * @date 2025-07-23
 *
 * @copyright Copyright (c) 2025 LDS
 *
 * 协议格式:
 * Head(2) + CMD(1) + SEQ(1) + LEN(2) + DATA(可变) + CRC16(2)
 * - 大端字节序（MSB在前）
 * - Head: 所有帧固定为0x5AA5
 * - SEQ: 0x00-0xFF序列号，用于请求/响应匹配
 * - CMD: 定义操作的功能码
 * - LEN: DATA字段的字节长度
 * - DATA: 可变长度载荷数据
 * - CRC16: 16位CRC校验和（从Head到CRC16-1所有字节的CRC16）
 */

#ifndef __APPLICATIONS_LDS_SMART_BASE_H__
#define __APPLICATIONS_LDS_SMART_BASE_H__

/* ================================ Includes ================================ */
#include <stdint.h>
#include <stdbool.h>
#include <rtthread.h>

#ifdef __cplusplus
extern "C" {
#endif

/* ================================ Macros ================================== */

/* ================================ Protocol Constants ===================== */
#define SMART_BASE_FRAME_HEAD1          0x5A
#define SMART_BASE_FRAME_HEAD2          0xA5
#define SMART_BASE_MIN_FRAME_LEN       8      /**< 不含DATA的最小帧长度 */
#define SMART_BASE_MAX_FRAME_LEN       64     /**< 最大帧长度 */
#define SMART_BASE_MAX_DATA_LEN        (SMART_BASE_MAX_FRAME_LEN - SMART_BASE_MIN_FRAME_LEN)

/* ================================ 类型定义 ======================== */

/**
 * @brief 智能底座命令枚举
 * @details 定义智能底座通信支持的命令类型
 */
typedef enum {
    LDS_SMART_BASE_CMD_KEY = 0x01,          /**< PPT按键命令 */
    LDS_SMART_BASE_CMD_VERSION = 0x02,      /**< 查询版本信息命令 */
    LDS_SMART_BASE_CMD_STATUS = 0x03,       /**< 查询状态信息命令 */
    LDS_SMART_BASE_CMD_MAX,
} LDS_SMART_BASE_CMD_E;

/**
 * @brief 协议帧结构体
 * @details 表示完整协议帧的结构体
 */
typedef struct {
    uint8_t head1;                           /**< 帧头（0x5A） */
    uint8_t head2;                           /**< 帧头（0xA5） */
    uint8_t cmd;                           /**< 命令码（大端序） */
    uint8_t seq;                            /**< 序列号 */
    uint16_t dataLen;                       /**< 数据长度（大端序） */
    uint8_t data[SMART_BASE_MAX_DATA_LEN];  /**< 数据载荷 */
    uint16_t crc16;                         /**< 帧CRC16校验和 */
} lds_smart_base_frame_t;

/* ================================ 函数声明 =================== */

/**
 * @brief 获取智能底座版本信息
 * @details 检索智能底座设备的版本信息
 *
 * @return const char* 指向版本字符串的指针
 */
const char *ldsSmartBaseGetVersion(void);
/**
 * @brief 初始化智能底座通信系统
 * @details 初始化硬件、UART通信、定时器和状态机
 *
 * @return int 成功返回0，失败返回负错误码
 *
 * @note 此函数执行完整的智能底座系统初始化，包括：
 *       - 电源控制引脚设置
 *       - 带回调的UART接口初始化
 *       - 心跳和命令重传的定时器配置
 *       - 线程安全的互斥锁初始化
 *       - 状态机和命令队列初始化
 *
 * @example
 * @code
 * int result = ldsSmartBaseInit();
 * if (result != 0) {
 *     rt_kprintf("Smart base initialization failed: %d\n", result);
 * }
 * @endcode
 */
int ldsSmartBaseInit(void);

/**
 * @brief 反初始化智能底座通信系统
 * @details 清理所有资源并停止通信
 *
 * @return int 成功返回0，失败返回负错误码
 *
 * @note 此函数应在系统关闭前或不再需要智能底座通信时调用
 */
int ldsSmartBaseDeinit(void);

/**
 * @brief 查询设备版本信息
 * @details 从指定设备请求版本信息
 *
 * @return int 成功返回0，失败返回负错误码
 *
 * @note 响应将在数据字段中包含版本信息
 */
int ldsSmartBaseQueryVersion(void);

/**
 * @brief 查询设备状态信息
 * @details 从指定设备请求状态信息
 *
 * @return int 成功返回0，失败返回负错误码
 *
 * @note 响应将在数据字段中包含状态信息
 */
int ldsSmartBaseQueryStatus(void);

/**
 * @brief 设置智能底座选择模式
 * @details 设置智能底座选择模式
 *
 * @param mode 选择模式
 * @return int 成功返回0，失败返回负错误码
 */

int ldsSmartBaseSetSelectMode(int8_t mode);
/**
 * @brief 获取智能底座状态
 * @details 获取智能底座状态
 *
 * @return uint8_t 状态值
 */
uint8_t ldsSmartBaseGetStatus(void);
/**
 * @brief 获取智能底座电源状态
 * @details 检索智能底座的当前电源状态
 *
 * @return int 成功时返回0或1，失败时返回负错误码
 */
int ldsSmartBaseGetPowerCtrl(void);
/**
 * @brief 控制智能底座电源
 * @details 通过电源控制引脚打开或关闭智能底座设备
 *
 * @param on true表示打开，false表示关闭
 * @return int 成功返回0，失败返回负错误码
 */
int ldsSmartBaseSetPowerCtrl(bool on);
#ifdef __cplusplus
}
#endif

#endif /* __APPLICATIONS_LDS_SMART_BASE_H__ */
