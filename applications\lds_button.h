/**
 * @file lds_button.h
 * @brief 按键驱动头文件
 * <AUTHOR> @date 
 * @version 
 * @copyright 
 */

#ifndef __APPLICATIONS_LDS_BUTTON_H__
#define __APPLICATIONS_LDS_BUTTON_H__

#include <stdint.h>
#include <stdbool.h>

#define LDS_BTN_SCAN_FREQ_HZ 50 // ldsButtonScan() 调用频率
#define LDS_MS_TO_SCAN_CNT(ms) ((ms) / (1000 / LDS_BTN_SCAN_FREQ_HZ))

/* 多击间隔，默认300ms */
#define MAX_MULTIPLE_CLICKS_INTERVAL (LDS_MS_TO_SCAN_CNT(300))

typedef void (*ldsButtonEventCallback_t)(void *);

typedef enum
{
    LDS_BTN_PRESS_DOWN = 0,           // 按下
    LDS_BTN_PRESS_CLICK,              // 单击
    LDS_BTN_PRESS_DOUBLE_CLICK,       // 双击
    LDS_BTN_PRESS_REPEAT_CLICK,       // 连击
    LDS_BTN_PRESS_SHORT_START,        // 短按开始
    LDS_BTN_PRESS_SHORT_UP,           // 短按释放
    LDS_BTN_PRESS_LONG_START,         // 长按开始
    LDS_BTN_PRESS_LONG_UP,            // 长按释放
    LDS_BTN_PRESS_LONG_HOLD,          // 长按保持
    LDS_BTN_PRESS_LONG_HOLD_UP,       // 长按保持释放
    LDS_BTN_PRESS_MAX,
    LDS_BTN_PRESS_NONE                // 无事件
} LDS_BUTTON_EVENT_E;

/**
 * @struct lds_button_t
 * @brief 按键数据结构体
 * @note 需要用户初始化的成员已标注
 */
typedef struct lds_button_t
{
    struct lds_button_t *next; // 内部使用，单向链表，指向下一个按键

    uint8_t (*usrButtonRead)(void *); // 用户自定义读取按键值的函数
    ldsButtonEventCallback_t cb;      // 按键事件回调函数
    void *usrData;                    // 用户数据指针，用户可自定义
    uint16_t scanCnt;                 // 内部使用，用户只读，按下时扫描计数
    uint16_t clickCnt;                // 内部使用，用户只读，按键点击次数
    uint16_t maxMultipleClicksInterval; // 多击间隔，默认MAX_MULTIPLE_CLICKS_INTERVAL

    uint16_t debounceTick;            // 消抖，暂未使用
    uint16_t shortPressStartTick;     // 短按起始时间，需用户配置
    uint16_t longPressStartTick;      // 长按起始时间，需用户配置
    uint16_t longHoldStartTick;       // 长按保持起始时间，需用户配置
    
    uint8_t id;                       // 按键ID，需用户配置，唯一
    uint8_t pressedLogicLevel : 1;    // 按下时的电平，需用户配置
    uint8_t event            : 4;     // 内部使用，当前按键事件
    uint8_t status           : 3;     // 内部使用，当前按键状态
} lds_button_t;

#ifdef __cplusplus
extern "C" {
#endif
/**
 * @brief 注册一个用户按键
 *
 * @param button: 按键结构体实例
 * @return 已注册的按键数量，错误时返回-1
 */
int32_t ldsButtonRegister(lds_button_t *button);
/**
 * @brief 读取指定按键的事件
 *
 * @param button: 按键结构体实例
 * @return 按键事件
 */
LDS_BUTTON_EVENT_E ldsButtonEventRead(const lds_button_t *button);
/**
 * @brief 启动按键扫描，需在指定周期内循环调用，建议周期5-20ms
 *
 * @return 激活的按键数量
 */
uint8_t ldsButtonScan(void);

#ifdef __cplusplus
}
#endif

#endif
