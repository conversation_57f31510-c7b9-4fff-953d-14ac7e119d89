/**
 * @file lds_kvdb.h
 * <AUTHOR> name (<EMAIL>)
 * @brief 
 * @version 0.1
 * @date 2025-06-04
 * 
 * @copyright Copyright (c) 2025
 * 
 */

#ifndef __LDS_KVDB_H__
#define __LDS_KVDB_H__

#ifdef __cplusplus
extern "C" {
#endif

/**
 * @brief 在键值数据库中存储数据
 *
 * @param key 用于标识数据的键字符串
 * @param data 指向要存储数据的指针
 * @param len 数据的字节长度
 *
 * @return 成功返回0，错误返回负值
 */

int ldsKvdbSet(const char *key, const void *data, uint32_t len);
/**
 * @brief 从键值数据库中检索数据
 *
 * @param key 要在数据库中查找的键字符串
 * @param data 指向存储检索数据的缓冲区的指针
 * @param len 数据缓冲区的字节大小
 *
 * @return int 成功返回0，失败返回负值
 */
int ldsKvdbGet(const char *key, const void *data, uint32_t len);

/**
 * @brief 初始化LDS键值数据库
 *
 * 此函数初始化LDS键值数据库系统。
 * 应在系统启动期间调用一次，在任何其他KVDB操作之前。
 *
 * @return int 成功返回0，错误返回负值
 */
int ldsKvdbInit(void);

#ifdef __cplusplus
}
#endif

#endif /* __LDS_KVDB_H__ */